{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a slideshow room section with dual images layout.

  Accepts:
  - full_width {boolean} - Whether to make the section full width
  - autoplay {boolean} - Whether to autoplay the slideshow
  - autoplay_speed {5-10} - The speed at which the slideshow should autoplay
  - style {'minimal'|'arrows'|'bars'|'dots'} - The style of the slideshow
  - height {450-750} - The height of the slideshow
  - height_mobile {350-650} - The height of the slideshow on mobile
  - hydration {string} - The hydration strategy for the section

  Features:
  - Left image: 647px × 647px
  - Right image: 1015px × 647px
  - Gap between images: 58px
  - Responsive design for mobile and tablet

  Usage:
  {% render 'section-slideshow-room' %}
{%- endcomment -%}

{%- liquid
  assign full_width = full_width | default: section.settings.full_width, allow_false: true | default: true, allow_false: true
  assign autoplay = autoplay | default: section.settings.autoplay, allow_false: true | default: false, allow_false: true
  assign autoplay_speed = autoplay_speed | default: section.settings.autoplay_speed | default: 7
  assign style = style | default: section.settings.style | default: 'arrows'
  assign height = height | default: section.settings.height | default: 650
  assign height_mobile = height_mobile | default: section.settings.height_mobile | default: 450
  assign hydration = hydration | default: 'on:visible'

  assign lazyload_images = true
  if section.index == 1
    assign lazyload_images = false
  endif
-%}

<is-land {{ hydration }}>
  <slideshow-section section-id="{{ section.id }}">
    {%- unless full_width -%}
      <div class="page-width hero--padded">
    {%- endunless -%}

    {% style %}
      .hero--{{ section.id }} {
        height: {{ height }}px;
      }

      @media screen and (max-width: 768px) {
        .hero--{{ section.id }} {
          height: {{ height_mobile }}px;
        }
      }

      /* Dual images layout styles */
      .hero__dual-images {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 58px;
        padding: 0 20px;
      }

      .hero__left-image {
        width: 647px;
        height: 647px;
        overflow: hidden;
        flex-shrink: 0;
      }

      .hero__right-image {
        width: 1015px;
        height: 647px;
        overflow: hidden;
        flex-shrink: 0;
      }

      .hero__left-image .hero__image,
      .hero__right-image .hero__image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .hero__image-link {
        display: block;
        width: 100%;
        height: 100%;
      }

      @media screen and (max-width: 1200px) {
        .hero__dual-images {
          gap: 30px;
          padding: 0 15px;
        }

        .hero__left-image {
          width: 400px;
          height: 400px;
        }

        .hero__right-image {
          width: 600px;
          height: 400px;
        }
      }

      @media screen and (max-width: 768px) {
        .hero__dual-images {
          flex-direction: column;
          gap: 20px;
          padding: 20px;
        }

        .hero__left-image,
        .hero__right-image {
          width: 100%;
          height: 250px;
        }
      }
    {% endstyle %}

    <div id="SlideshowWrapper-{{ section.id }}">
      {%- if section.blocks.size > 0 -%}
        <div class="slideshow-wrapper">
          {%- if autoplay and style == 'bars' and section.blocks.size > 1 -%}
            {%- style -%}
              [data-bars][data-autoplay="true"] .flickity-page-dots .dot:after {
                animation-duration: {{ autoplay_speed | times: 1000 }}ms;
              }
            {%- endstyle -%}

            <button
              type="button"
              class="visually-hidden slideshow__pause"
              data-id="{{ section.id }}"
              aria-live="polite"
            >
              <span class="slideshow__pause-stop">
                {% render 'icon', name: 'pause' %}
                <span class="icon__fallback-text visually-hidden">
                  {% render 't_with_fallback', key: 'actions.slideshow_pause', fallback: 'Slideshow pause' -%}
                </span>
              </span>
              <span class="slideshow__pause-play">
                {% render 'icon', name: 'play' %}
                <span class="icon__fallback-text visually-hidden">
                  {% render 't_with_fallback', key: 'actions.slideshow_play', fallback: 'Slideshow play' -%}
                </span>
              </span>
            </button>
          {%- endif -%}

          <div
            id="Slideshow-{{ section.id }}"
            class="hero hero--{{ section.id }}{% if section.index == 1 %} loaded{% else %} loading{% endif %} loading--delayed"
            data-autoplay="{{ autoplay }}"
            data-speed="{{ autoplay_speed | times: 1000 }}"
            {% if style == 'arrows' %}
              data-arrows="true"
            {% endif %}
            {% if style == 'dots' %}
              data-dots="true"
            {% endif %}
            {% if style == 'bars' %}
              data-dots="true"
              data-bars="true"
            {% endif %}
            data-slide-count="{{ section.blocks.size }}"
          >
            {%- for block in section.blocks -%}
              <div
                {{ block.shopify_attributes }}
                class="slideshow__slide slideshow__slide--{{ block.id }}{% if section.index == 1 and forloop.index == 1 %} is-selected{% endif %}"
                data-index="{{ forloop.index0 }}"
                data-id="{{ block.id }}"
              >


                <div class="hero__dual-images">
                  <div class="hero__left-image">
                    {%- if block.settings.image != blank -%}
                      {%- if block.settings.link != blank -%}
                        <a href="{{ block.settings.link }}" class="hero__image-link" aria-hidden="true">
                      {%- endif -%}

                      {%- capture image_classes -%}
                          hero__image hero__image--{{ block.id }}
                        {%- endcapture -%}

                      {%- liquid
                        if forloop.index == 1
                          assign loading = lazyload_images
                        else
                          assign loading = true
                        endif
                      -%}
                      {%- render 'image-element',
                        img: block.settings.image,
                        loading: loading,
                        classes: image_classes,
                        sizeVariable: '647px'
                      -%}

                      {%- if block.settings.link != blank -%}
                        </a>
                      {%- endif -%}
                    {%- else -%}
                      {%- render 'placeholder-svg', name: 'lifestyle-1' -%}
                    {%- endif -%}
                  </div>
                  <div class="hero__right-image">
                    {%- if block.settings.right_image != blank -%}
                      {%- if block.settings.right_link != blank -%}
                        <a href="{{ block.settings.right_link }}" class="hero__image-link" aria-hidden="true">
                      {%- endif -%}

                      {%- capture right_image_classes -%}
                          hero__image hero__image--right-{{ block.id }}
                        {%- endcapture -%}

                      {%- liquid
                        if forloop.index == 1
                          assign loading = lazyload_images
                        else
                          assign loading = true
                        endif
                      -%}
                      {%- render 'image-element',
                        img: block.settings.right_image,
                        loading: loading,
                        classes: right_image_classes,
                        sizeVariable: '1015px'
                      -%}

                      {%- if block.settings.right_link != blank -%}
                        </a>
                      {%- endif -%}
                    {%- else -%}
                      {%- render 'placeholder-svg', name: 'lifestyle-2' -%}
                    {%- endif -%}
                  </div>
                </div>
              </div>
            {%- endfor -%}
          </div>
        </div>
      {%- endif -%}

      {%- render 'placeholder-noblocks' -%}
    </div>
    {%- unless full_width -%}
      </div>
    {%- endunless -%}
  </slideshow-section>

  <template data-island>
    <script type="module">
      import '@archetype-themes/modules/slideshow'
    </script>
  </template>
</is-land>
