.at-add-to-cart {
    display: block;
    position: relative;
    height: 36px
}

.at-add-to-cart.is-added {
    pointer-events: none
}

.at-add-to-cart__button.btn,
.at-add-to-cart__content {
    display: flex;
    padding: 7px 24px;
    justify-content: center;
    align-items: center;
    border-radius: 32px;
    width: 100%;
    height: 100%
}

.at-add-to-cart__button--add.btn:hover,
.at-add-to-cart__button--options.btn:hover,
.at-add-to-cart__button--add.btn:focus,
.at-add-to-cart__button--options.btn:focus {
    color: var(--color-button-primary-text);
    background-color: var(--color-button-primary)
}

.at-add-to-cart__content {
    position: absolute;
    top: 0;
    left: 0;
    display: none;
    background: var(--color-button-primary)
}

.at-add-to-cart__button[aria-expanded='true']+.at-add-to-cart__content {
    display: flex
}

.at-add-to-cart__content .js-qty__wrapper {
    max-width: none;
    width: 100%;
    background: #fff0
}

.is-added .at-add-to-cart__content .js-qty__wrapper {
    display: none
}

.at-add-to-cart__content .js-qty__num {
    border: none;
    padding: 0 16px;
    background: #fff0;
    color: var(--color-button-primary-text);
    font-size: calc(var(--type-base-size));
    font-weight: var(--type-header-weight);
    line-height: 1.42
}

.at-add-to-cart__content .js-qty__adjust {
    padding: 0;
    color: var(--color-button-primary-text)
}

.at-add-to-cart__content .js-qty__adjust:hover,
.at-add-to-cart__content .js-qty__adjust:focus {
    background: #fff0;
    color: var(--color-button-primary-text)
}

.at-add-to-cart__content .js-qty__adjust .icon {
    width: 16px;
    height: 16px
}

.at-add-to-cart__message.btn {
    position: absolute;
    top: 0;
    left: 0;
    display: none;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    height: 36px;
    border-radius: 32px;
    cursor: default
}

.is-added .at-add-to-cart__message.btn {
    display: flex
}

:root {
    --size-0-5: 0.125rem;
    --size-1: 0.25rem;
    --size-1-5: 0.375rem;
    --size-2: 0.5rem;
    --size-2-5: 0.625rem;
    --size-3: 0.75rem;
    --size-3-5: 0.875rem;
    --size-4: 1rem;
    --size-4-5: 1.125rem;
    --size-5: 1.25rem;
    --size-5-5: 1.375rem;
    --size-6: 1.5rem;
    --size-6-5: 1.625rem;
    --size-7: 1.75rem;
    --size-7-5: 1.875rem;
    --size-8: 2rem;
    --size-8-5: 2.125rem;
    --size-9: 2.25rem;
    --size-9-5: 2.375rem;
    --size-10: 2.5rem;
    --size-11: 2.75rem;
    --size-12: 3rem;
    --size-14: 3.5rem;
    --size-16: 4rem;
    --size-18: 4.5rem;
    --size-20: 5rem;
    --size-24: 6rem;
    --size-28: 7rem;
    --size-32: 8rem;
    --gutter: 30px;
    --page-width: 1500px;
    --page-width-narrow: 1000px;
    --input-radius: 0;
    --page-width-gutter-small: 17px;
    --grid-gutter: 22px;
    --grid-gutter-small: 16px;
    --slide-curve: 0.25s cubic-bezier(0.165, 0.84, 0.44, 1);
    --drawer-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    --product-grid-margin: 10px;
    --product-grid-padding: 12px;
    --product-radius: 10px;
    --color-sticky-nav-links: #fff;
    --disabled-grey: #f6f6f6;
    --disabled-border: #b6b6b6;
    --disabled-grey-text: #b6b6b6;
    --error-red: #d02e2e;
    --error-red-bg: #fff6f6;
    --success-green: #56ad6a;
    --success-green-bg: #ecfef0;
    --desktop-menu-chevron-size: 10px;
    --site-nav-item-padding: 20px;
    --site-nav-item-padding-top-bottom: 16px;
    --site-nav-icon-padding: 12px;
    --z-index-modal: 30;
    --z-index-toolbar: 7;
    --z-index-header: 6;
    --z-index-header-submenu: 7;
    --z-index-header-bottom-row: 3;
    --z-index-header-drawers: 5;
    --z-index-header-drawers-mobile: 3;
    --z-index-header-search: 7;
    --z-index-loader: 4;
    --z-index-header-search-overlay: 1
}

*,
::before,
::after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

*,
::before,
::after {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: #e5e7eb
}

::before,
::after {
    --tw-content: ''
}

html,
:host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-feature-settings: normal;
    font-variation-settings: normal;
    -webkit-tap-highlight-color: #fff0
}

body {
    margin: 0;
    line-height: inherit
}

hr {
    height: 0;
    color: inherit;
    border-top-width: 1px
}

abbr:where([title]) {
    text-decoration: underline;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
samp,
pre {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-feature-settings: normal;
    font-variation-settings: normal;
    font-size: 1em
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse
}

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    color: inherit;
    margin: 0;
    padding: 0
}

button,
select {
    text-transform: none
}

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
    -webkit-appearance: button;
    background-color: #fff0;
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto
}

[type='search'] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
    margin: 0
}

fieldset {
    margin: 0;
    padding: 0
}

legend {
    padding: 0
}

ol,
ul,
menu {
    list-style: none;
    margin: 0;
    padding: 0
}

dialog {
    padding: 0
}

textarea {
    resize: vertical
}

input::placeholder,
textarea::placeholder {
    opacity: 1;
    color: #9ca3af
}

button,
[role="button"] {
    cursor: pointer
}

:disabled {
    cursor: default
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
    display: block;
    vertical-align: middle
}

img,
video {
    max-width: 100%;
    height: auto
}

[hidden]:where(:not([hidden="until-found"])) {
    display: none
}

html {
    touch-action: manipulation
}

html[dir='rtl'] {
    direction: rtl
}

html,
body {
    background-color: var(--color-body);
    color: var(--color-text-body)
}

@media only screen and (max-width:768px) {
    @media (hover:none) and (pointer:coarse) {
        .lock-scroll {
            overflow: hidden !important;
            touch-action: none;
            -webkit-overflow-scrolling: auto
        }
    }
}

.page-width {
    max-width: none;
    margin: 0 auto;
}

.page-width {
    padding: 0 100px
}

.page-full {
    padding: 0 var(--page-width-padding)
}

.page-width--narrow {
    max-width: var(--page-narrow)
}

.page-width--tiny {
    max-width: 450px
}

@media only screen and (max-width:768px) {
    .page-width--flush-small {
        padding: 0
    }
}

.page-content,
.shopify-policy__container,
.shopify-email-marketing-confirmation__container {
    padding-top: var(--page-top-padding);
    padding-bottom: var(--page-top-padding)
}

.shopify-email-marketing-confirmation__container {
    text-align: center
}

hr,
.hr--small,
.hr--medium,
.hr--large {
    height: 1px;
    border: 0;
    border-top: 1px solid;
    border-top-color: var(--color-border)
}

.hr--small {
    margin: 15px auto
}

.hr--medium {
    margin: 30px auto
}

.hr--large {
    margin: var(--gutter) auto
}

@media only screen and (min-width:769px) {
    .hr--large {
        margin: calc(var(--gutter) * 1.5) auto
    }
}

.page-blocks+.hr--large,
.page-blocks+[data-section-type='recently-viewed'] .hr--large {
    margin-top: 0
}

.hr--clear {
    border: 0
}

@media only screen and (max-width:768px) {
    .table--responsive thead {
        display: none
    }

    .table--responsive tr {
        display: block
    }

    .table--responsive tr,
    .table--responsive td {
        float: left;
        clear: both;
        width: 100%
    }

    .table--responsive th,
    .table--responsive td {
        display: block;
        text-align: right;
        padding: 15px
    }

    .table--responsive td:before {
        content: attr(data-label);
        float: left;
        font-size: 12px;
        padding-right: 10px
    }
}

@media only screen and (max-width:768px) {
    .table--small-hide {
        display: none !important
    }

    .table__section+.table__section {
        position: relative;
        margin-top: 10px;
        padding-top: 15px
    }

    .table__section+.table__section:after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: 15px;
        right: 15px;
        border-bottom: 1px solid;
        border-bottom-color: var(--color-border)
    }
}

details summary::-webkit-details-marker {
    display: none
}

@keyframes spin {
    0% {
        transform: rotate(0deg)
    }

    100% {
        transform: rotate(360deg)
    }
}

@keyframes preloading {
    0% {
        transform-origin: 0 50%;
        transform: scale3d(0, 1, 1);
        opacity: 0
    }

    40% {
        transform-origin: 0 50%;
        transform: scale3d(1, 1, 1);
        opacity: 1
    }

    41% {
        transform-origin: 100% 50%;
        transform: scale3d(1, 1, 1);
        opacity: 1
    }

    100% {
        transform-origin: 100% 50%;
        transform: scale3d(0, 1, 1);
        opacity: 1
    }
}

@keyframes slideshowBars {
    from {
        transform: translateX(-100%)
    }

    to {
        transform: translateX(0)
    }
}

@keyframes grid-product__loading {
    0% {
        opacity: 1
    }

    60% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

@keyframes overlay-on {
    from {
        opacity: 0
    }

    to {
        opacity: .6
    }
}

@keyframes overlay-off {
    from {
        opacity: .6
    }

    to {
        opacity: 0
    }
}

@keyframes modal-open {
    from {
        opacity: 0;
        transform: scale(.97)
    }

    to {
        opacity: 1;
        transform: scale(1)
    }
}

@keyframes modal-closing {
    from {
        opacity: 1;
        transform: scale(1)
    }

    to {
        opacity: 0;
        transform: scale(.97)
    }
}

@keyframes rise-up {
    from {
        opacity: 0;
        transform: translateY(10px)
    }

    to {
        opacity: 1;
        transform: translateY(0)
    }
}

@keyframes fade-in {
    from {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes fade-out {
    from {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

@keyframes zoom-fade {
    from {
        opacity: 0;
        transform: scale(1.1, 1.1)
    }

    to {
        opacity: 1;
        transform: scale(1, 1)
    }
}

@keyframes placeholder-shimmer {
    0% {
        background-position: -150% 0
    }

    100% {
        background-position: 150% 0
    }
}

*,
input,
:before,
:after {
    box-sizing: border-box
}

html,
body {
    padding: 0;
    margin: 0
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
    display: block
}

audio,
canvas,
progress,
video {
    display: inline-block;
    vertical-align: baseline
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
    height: auto
}

input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-decoration {
    -webkit-appearance: none
}

body,
input,
textarea,
button,
select,
.faux-select {
    font-family: var(--type-base-primary), var(--type-base-fallback);
    font-size: var(--type-base-size);
    letter-spacing: var(--type-base-spacing);
    line-height: var(--type-base-line-height);
    -webkit-font-smoothing: antialiased;
    -webkit-text-size-adjust: 100%;
    text-rendering: optimizeSpeed
}

body {
    font-weight: var(--type-base-weight)
}

p {
    margin: 0 0 calc(var(--gutter) / 2) 0
}

p:last-child {
    margin-bottom: 0
}

p img {
    margin: 0
}

em {
    font-style: italic
}

b,
strong {
    font-weight: 700
}

small,
p[data-spam-detection-disclaimer] {
    font-size: .85em
}

sup,
sub {
    position: relative;
    font-size: 60%;
    vertical-align: baseline
}

sup {
    top: -.5em
}

sub {
    bottom: -.5em
}

blockquote,
.rte blockquote {
    margin: 0;
    padding: calc(var(--gutter) / 2) var(--gutter) 40px
}

blockquote p,
.rte blockquote p {
    margin-bottom: 0
}

blockquote p+cite,
.rte blockquote p+cite {
    margin-top: calc(var(--gutter) / 2)
}

blockquote cite,
.rte blockquote cite {
    display: block
}

code,
pre {
    background-color: #faf7f5;
    font-family: Consolas, monospace;
    font-size: 1em;
    border: 0 none;
    padding: 0 2px;
    color: #51ab62
}

pre {
    overflow: auto;
    padding: calc(var(--gutter) / 2);
    margin: 0 0 var(--gutter)
}

label:not(.variant__button-label):not(.text-label),
.label {
    font-weight: var(--type-header-weight);
    font-size: calc(var(--type-base-size) + 1px)
}

label {
    display: block;
    margin-bottom: 10px
}

.text-label a {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.label-info {
    display: block;
    margin-bottom: 10px
}

.h0,
.heading-font-stack,
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
    font-family: var(--type-header-primary), var(--type-header-fallback);
    font-weight: var(--type-header-weight);
    letter-spacing: var(--type-header-spacing);
    line-height: var(--type-header-line-height);
    text-transform: var(--type-header-transform)
}

.h0,
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
    display: block;
    margin: 0 0 15px
}

@media only screen and (min-width:769px) {

    .h0,
    h1,
    .h1,
    h2,
    .h2,
    h3,
    .h3,
    h4,
    .h4,
    h5,
    .h5,
    h6,
    .h6 {
        margin: 0 0 20px
    }
}

.h0 a,
h1 a,
.h1 a,
h2 a,
.h2 a,
h3 a,
.h3 a,
h4 a,
.h4 a,
h5 a,
.h5 a,
h6 a,
.h6 a {
    -webkit-text-decoration: none;
    text-decoration: none;
    font-weight: inherit
}

.h0 {
    font-size: var(--type-header-size);
    font-family: var(--type-header-primary), var(--type-header-fallback);
    font-weight: var(--type-header-weight);
    letter-spacing: var(--type-header-spacing);
    line-height: var(--type-header-line-height)
}

h1,
.h1 {
    font-size: calc(var(--type-header-size) * 0.85)
}

h2,
.h2 {
    font-size: calc(var(--type-header-size) * 0.63)
}

h3,
.h3 {
    font-size: calc(var(--type-header-size) * 0.57)
}

h4,
.h4 {
    font-size: calc(var(--type-header-size) * 0.55)
}

@media only screen and (min-width:769px) {
    .h0 {
        font-size: calc(var(--type-header-size) * 1.25)
    }

    h1,
    .h1 {
        font-size: var(--type-header-size)
    }

    h2,
    .h2 {
        font-size: calc(var(--type-header-size) * 0.85)
    }

    h3,
    .h3 {
        font-size: calc(var(--type-header-size) * 0.7)
    }

    .h3--mobile {
        font-size: calc(var(--type-header-size) * 0.57)
    }
}

.accent-subtitle {
    letter-spacing: .07em;
    margin: 0 0 20px
}

.text-spacing,
.text-spacing.rte:last-child {
    margin-bottom: 15px
}

@media only screen and (min-width:769px) {

    .text-spacing,
    .text-spacing.rte:last-child {
        margin-bottom: 25px
    }
}

@media only screen and (max-width:768px) {

    .rte table td,
    .rte table th {
        padding: 6px 8px
    }
}

.collapsible-content .rte table td,
.collapsible-content .rte table th {
    padding: 6px 8px
}

.enlarge-text {
    font-size: calc(var(--type-base-size) + 2px)
}

@media only screen and (min-width:769px) {
    .enlarge-text {
        font-size: calc(var(--type-base-size) + 4px)
    }
}

@media only screen and (min-width:769px) {
    .table--small-text {
        font-size: calc(var(--type-base-size) * 0.85)
    }
}

.index-section--footer h3 {
    font-size: 1.5em
}

html[dir='rtl'] .text-left {
    text-align: right !important
}

html[dir='rtl'] .text-right {
    text-align: left !important
}

.icon-and-text {
    display: flex;
    flex-wrap: nowrap;
    align-items: center
}

.icon-and-text .icon {
    flex: 0 0 auto
}

.capitalize-first-letter:first-letter {
    font-size: 55px;
    float: left;
    margin-right: 6px;
    margin-top: -20px;
    font-weight: var(--type-header-weight)
}

.text-highlight em {
    font-style: normal
}

.text-highlight.h0 a,
.text-highlight.h1 a,
.text-highlight.h2 a,
.text-highlight.h3 a,
.text-highlight.h4 a,
.text-highlight.h5 a,
.text-highlight.h6 a {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.text-highlight--outline em {
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: inherit;
    -webkit-text-fill-color: var(--color-body)
}

.color-scheme-1 .text-highlight--outline em {
    -webkit-text-fill-color: var(--color-scheme-1-bg)
}

.color-scheme-2 .text-highlight--outline em {
    -webkit-text-fill-color: var(--color-scheme-2-bg)
}

.color-scheme-3 .text-highlight--outline em {
    -webkit-text-fill-color: var(--color-scheme-3-bg)
}

.text-highlight--regular em {
    font-style: italic
}

.text-highlight--serif em {
    font-family: serif;
    font-style: italic
}

.text-highlight--handwrite em {
    font-family: cursive;
    font-style: italic
}

.text-highlight--accent-color em {
    color: var(--color-sale-tag)
}

.base-extra-small-font-stack {
    font-size: max(calc(var(--type-base-size) * 0.7), 12px)
}

[class*='color-scheme-']:not(.color-scheme-none) {
    position: relative;
    z-index: 1
}

[class*='color-scheme-']:not(.color-scheme-none) a:not(.btn) {
    color: currentColor;
    border-color: currentColor
}

.index-section[class*='color-scheme-']:not(.color-scheme-none) {
    margin: 0;
    padding-top: var(--index-section-padding);
    padding-bottom: var(--index-section-padding)
}

.color-scheme-1 {
    color: var(--color-scheme-1-text);
    background-color: var(--color-scheme-1-bg)
}

.color-scheme-1 .btn {
    color: var(--color-scheme-1-bg);
    background-color: var(--color-scheme-1-text)
}

.color-scheme-1 .btn.color-scheme-reversed {
    background-color: var(--color-scheme-1-bg);
    color: var(--color-scheme-1-text);
    border-color: var(--color-scheme-1-text)
}

.color-scheme-2 {
    color: var(--color-scheme-2-text);
    background-color: var(--color-scheme-2-bg)
}

.color-scheme-2 .btn {
    color: var(--color-scheme-2-bg);
    background-color: var(--color-scheme-2-text)
}

.color-scheme-2 .btn.color-scheme-reversed {
    color: var(--color-scheme-2-text);
    background-color: var(--color-scheme-2-bg);
    border-color: var(--color-scheme-2-text)
}

.color-scheme-3 {
    color: var(--color-scheme-3-text);
    background-color: var(--color-scheme-3-bg)
}

.color-scheme-3 .btn {
    color: var(--color-scheme-3-bg);
    background-color: var(--color-scheme-3-text)
}

.color-scheme-3 .btn.color-scheme-reversed {
    color: var(--color-scheme-3-text);
    background-color: var(--color-scheme-3-bg);
    border-color: var(--color-scheme-3-text)
}

[class*='color-scheme-']:not(.color-scheme-none) input,
[class*='color-scheme-']:not(.color-scheme-none) textarea {
    color: var(--color-text-body);
    background-color: var(--color-body)
}

:root {
    --animate-duration: 1s
}

[data-animate] {
    opacity: 0;
    transition-property: opacity, transform;
    transition-duration: var(--animate-duration), var(--animate-duration);
    transition-timing-function: ease-in-out, ease-in-out
}

.no-js [data-animate] {
    opacity: 1
}

[data-animate] {
    opacity: 0
}

[ready] [data-animate] {
    opacity: 1
}

[data-animate='fadein'] {
    opacity: 0
}

[ready] [data-animate='fadein'] {
    opacity: 1
}

[data-animate='fadeup'] {
    transform: translateY(5rem)
}

[ready] [data-animate='fadeup'] {
    transform: translateY(0)
}

[data-animate='fadedown'] {
    transform: translateY(-5rem)
}

[ready] [data-animate='fadedown'] {
    transform: translateY(0)
}

[data-animate='fadeleft'] {
    transform: translateX(-5rem)
}

[ready] [data-animate='fadeleft'] {
    transform: translateX(0)
}

[data-animate='faderight'] {
    transform: translateX(5rem)
}

[ready] [data-animate='faderight'] {
    transform: translateX(0)
}

[data-animate='zoomin'] {
    transform: scale(.8)
}

[ready] [data-animate='zoomin'] {
    transform: scale(1)
}

[data-animate='zoomout'] {
    transform: scale(1.2)
}

[ready] [data-animate='zoomout'] {
    transform: scale(1)
}

[data-animate='rise-up'] {
    opacity: 0
}

[data-animate='rise-up'] [ready] [data-animate='rise-up'] {
    animation: rise-up 0.8s cubic-bezier(.26, .54, .32, 1) forwards
}

.image-wrap {
    background: var(--color-small-image-bg);
    overflow: hidden
}

.image-wrap img:not([role='presentation']) {
    display: block
}

.unload:before,
.unload:after {
    content: '';
    position: absolute;
    width: 100px;
    height: 3px;
    background: var(--color-body);
    left: 50%;
    top: 30%;
    margin: -1px 0 0 -50px;
    z-index: var(--z-index-loader);
    opacity: 0
}

.no-js .unload:before,
.no-js .unload:after {
    display: none
}

.unload:before {
    background: var(--color-text-body);
    opacity: 1
}

.unload:after {
    opacity: 0;
    animation: preloading 0.5s ease 0.3s infinite
}

.loading {
    animation: placeholder-shimmer 1.3s linear 0.5s infinite;
    background-size: 300% 100%;
    background-image: linear-gradient(100deg, var(--color-large-image-bg) 40%, var(--color-large-image-bg-light) 63%, var(--color-large-image-bg) 79%)
}

.loading.loaded {
    animation: none;
    background-image: none
}

.loading--delayed:before {
    animation-delay: 0.8s !important;
    animation-duration: 1s !important
}

.loading--delayed:after {
    animation-delay: 1.3s !important
}

.clearfix::after {
    clear: both;
    content: '';
    display: table
}

.is-transitioning {
    display: block !important;
    visibility: visible !important
}

.visually-hidden {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

.overlay::after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: var(--z-index-overlay, auto)
}

.visually-invisible {
    opacity: 0 !important
}

.show {
    display: block !important
}

.hide {
    display: none !important
}

.text-left {
    text-align: left !important
}

.text-right {
    text-align: right !important
}

.text-center {
    text-align: center !important
}

@media only screen and (max-width:768px) {
    .small--show {
        display: block !important
    }

    .small--hide {
        display: none !important
    }

    .small--text-left {
        text-align: left !important
    }

    .small--text-right {
        text-align: right !important
    }

    .small--text-center {
        text-align: center !important
    }
}

@media only screen and (min-width:769px) {
    .medium-up--hide {
        display: none !important
    }
}

.svg-mask is-land[data-image-type],
.image-wrap:not(.image-wrap__thumbnail) is-land[data-image-type],
.grid__image-ratio is-land[data-image-type],
.countdown__background-image-wrapper is-land[data-image-type],
.social-section__image-wrapper is-land[data-image-type],
.fading-images__item-wrapper is-land[data-image-type] {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0
}

img {
    border: 0 none;
    display: block;
    max-width: 100%;
    height: auto
}

.image-element {
    opacity: 0
}

.image-element[loading='eager'] {
    opacity: 1
}

.template-giftcard .image-element {
    opacity: 1
}

[data-animate_images='false'] .image-element {
    opacity: 1
}

[data-media-gallery-layout='stacked'] .image-element {
    opacity: 1
}

.image-element[data-animate='fadein'] {
    --animate-duration: 0.5s
}

[data-media-gallery-layout='stacked'] .image-element[data-animate='fadein'] {
    animation: none
}

.image-element.scheme-image[data-animate='fadein'] {
    animation: none
}

.image-element[data-animate='fadein'][loading='eager'] {
    animation: none
}

.image-element.hero__image[data-animate='fadein'] {
    animation: none
}

.no-js .image-element {
    opacity: 1
}

.image-wrap {
    position: relative
}

.image-wrap img {
    width: 100%;
    object-fit: cover;
    height: auto
}

.rte p[style*='text-align: center'] img,
.rte div[style*='text-align: center'] img {
    margin-left: auto;
    margin-right: auto
}

svg:not(:root) {
    overflow: hidden
}

iframe {
    max-width: 100%;
    border: none
}

.video-wrapper {
    position: relative;
    overflow: hidden;
    max-width: 100%
}

.video-wrapper iframe,
.video-wrapper video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.video-wrapper--modal {
    background-color: #000;
    width: 1000px
}

.grid__image-ratio {
    position: relative;
    background-color: var(--color-small-image-bg)
}

.grid__image-ratio img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover
}

.grid__image-ratio img.grid__image-contain {
    object-fit: contain
}

.grid__image-ratio:before {
    content: '';
    display: block;
    height: 0;
    width: 100%
}

.grid__image-ratio .placeholder-svg {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.grid__image-ratio--object {
    opacity: 1
}

.grid__image-ratio--wide:before {
    padding-bottom: 56.25%
}

.grid__image-ratio--landscape:before {
    padding-bottom: 75%
}

.grid__image-ratio--square:before {
    padding-bottom: 100%
}

.grid__image-ratio--portrait:before {
    padding-bottom: 150%
}

.image-fit {
    position: relative;
    width: 100%;
    height: 100%;
    object-fit: cover;
    font-family: 'object-fit: cover';
    z-index: 1
}

.parallax-container {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%
}

.svg-mask {
    background: transparent !important;
    background-color: transparent !important;
    padding-bottom: 100% !important;
    position: relative !important
}

.svg-mask .grid__image-ratio {
    background: transparent !important;
    background-color: transparent !important
}

.svg-mask img,
.svg-mask svg:not(.icon),
.svg-mask video {
    -webkit-mask-size: 100% 100%;
    mask-size: 100% 100%;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    height: 100% !important;
    position: absolute !important;
    object-fit: cover !important
}

.svg-mask--landscape {
    padding-bottom: 56.25% !important
}

.svg-mask--portrait {
    padding-bottom: 150% !important
}

.svg-mask--square {
    padding-bottom: 100% !important
}

.svg-mask--rounded-top img,
.svg-mask--rounded-top svg:not(.icon),
.svg-mask--rounded-top video {
    border-top-right-radius: 50%;
    border-top-left-radius: 50%
}

.svg-mask--rounded img,
.svg-mask--rounded svg:not(.icon),
.svg-mask--rounded video {
    border-radius: 50%
}

.svg-mask--star img,
.svg-mask--star svg:not(.icon),
.svg-mask--star video {
    -webkit-mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTI4NCcgaGVpZ2h0PScxMjUxJyB2aWV3Qm94PScwIDAgMTI4NCAxMjUxJyBmaWxsPSdub25lJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnPjxwYXRoIGQ9J002NDIgMEw4MTkuMDA3IDI5MC40NDFMMTE1Ni40NSAyNDcuNzQ0TDEwMzkuNzMgNTY3LjIyTDEyODMuNSA4MDQuNDE5TDk2MC45NTYgOTEyLjM1OUw5MjcuNDk1IDEyNTAuODRMNjQyIDEwNjUuOTZMMzU2LjUwNCAxMjUwLjg0TDMyMy4wNDQgOTEyLjM1OUwwLjQ5NzQzNyA4MDQuNDE5TDI0NC4yNjggNTY3LjIyTDEyNy41NTUgMjQ3Ljc0NEw0NjQuOTkzIDI5MC40NDFMNjQyIDBaJyBmaWxsPScjQTQ5NUZCJy8+PC9zdmc+Cg==);
    mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTI4NCcgaGVpZ2h0PScxMjUxJyB2aWV3Qm94PScwIDAgMTI4NCAxMjUxJyBmaWxsPSdub25lJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnPjxwYXRoIGQ9J002NDIgMEw4MTkuMDA3IDI5MC40NDFMMTE1Ni40NSAyNDcuNzQ0TDEwMzkuNzMgNTY3LjIyTDEyODMuNSA4MDQuNDE5TDk2MC45NTYgOTEyLjM1OUw5MjcuNDk1IDEyNTAuODRMNjQyIDEwNjUuOTZMMzU2LjUwNCAxMjUwLjg0TDMyMy4wNDQgOTEyLjM1OUwwLjQ5NzQzNyA4MDQuNDE5TDI0NC4yNjggNTY3LjIyTDEyNy41NTUgMjQ3Ljc0NEw0NjQuOTkzIDI5MC40NDFMNjQyIDBaJyBmaWxsPScjQTQ5NUZCJy8+PC9zdmc+Cg==)
}

.svg-mask--rounded-wave img,
.svg-mask--rounded-wave svg:not(.icon),
.svg-mask--rounded-wave video {
    -webkit-mask-image: url(data:image/svg+xml;base64,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);
    mask-image: url(data:image/svg+xml;base64,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)
}

.svg-mask--splat-1 img,
.svg-mask--splat-1 svg:not(.icon),
.svg-mask--splat-1 video {
    -webkit-mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTA0OCcgaGVpZ2h0PScxMDUyJyB2aWV3Qm94PScwIDAgMTA0OCAxMDUyJyBmaWxsPSdub25lJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnPjxwYXRoIGZpbGwtcnVsZT0nZXZlbm9kZCcgY2xpcC1ydWxlPSdldmVub2RkJyBkPSdNNTcwLjkwNSAxNTIuMzQ4QzY4OC40NDEgMTQwLjMzIDc3MS4xODcgLTI3LjkxNDggODg0Ljk0MSA0LjAyNTc1Qzk4Ny45OTcgMzIuOTYyMyAxMDA2LjIzIDE3OC4xODggMTAzMi42IDI4MS45ODVDMTA1NS43NiAzNzMuMTU0IDEwNTIuMjUgNDY4LjEzNCAxMDI1Ljc2IDU1OC4zODhDMTAwMS43IDY0MC4zMzYgOTQwLjI5OCA3MDAuNTM5IDg4OS41NTIgNzY5LjIxOUM4MzQuMDIyIDg0NC4zNzUgNzk1LjMgOTM0LjQ2IDcxMy45NSA5ODAuMzk3QzYyMi4zMTggMTAzMi4xNCA1MTAuMTA4IDEwNzMuNSA0MTAuNDM2IDEwMzkuNzdDMzExLjE4NiAxMDA2LjE4IDI3NS45MjcgODg3LjQwNSAyMDYuNjc3IDgwOC43MzdDMTM2LjcwMyA3MjkuMjQ4IDE0LjY1MzYgNjc5Ljk3OCAxLjIyNDE0IDU3NC45MDdDLTEyLjIxNDYgNDY5Ljc2MyA4OC4yODk2IDM4OC40MzMgMTQxLjIyMSAyOTYuNjEyQzE4OC42MTYgMjE0LjM5NCAyMDUuNjQzIDk0LjQ4MjEgMjk1LjU3NSA2NC4yODhDMzg4LjgwOSAzMi45ODUxIDQ3My4wNzEgMTYyLjM1MSA1NzAuOTA1IDE1Mi4zNDhaJyBmaWxsPScjQTQ5NUZCJy8+PC9zdmc+Cg==);
    mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTA0OCcgaGVpZ2h0PScxMDUyJyB2aWV3Qm94PScwIDAgMTA0OCAxMDUyJyBmaWxsPSdub25lJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnPjxwYXRoIGZpbGwtcnVsZT0nZXZlbm9kZCcgY2xpcC1ydWxlPSdldmVub2RkJyBkPSdNNTcwLjkwNSAxNTIuMzQ4QzY4OC40NDEgMTQwLjMzIDc3MS4xODcgLTI3LjkxNDggODg0Ljk0MSA0LjAyNTc1Qzk4Ny45OTcgMzIuOTYyMyAxMDA2LjIzIDE3OC4xODggMTAzMi42IDI4MS45ODVDMTA1NS43NiAzNzMuMTU0IDEwNTIuMjUgNDY4LjEzNCAxMDI1Ljc2IDU1OC4zODhDMTAwMS43IDY0MC4zMzYgOTQwLjI5OCA3MDAuNTM5IDg4OS41NTIgNzY5LjIxOUM4MzQuMDIyIDg0NC4zNzUgNzk1LjMgOTM0LjQ2IDcxMy45NSA5ODAuMzk3QzYyMi4zMTggMTAzMi4xNCA1MTAuMTA4IDEwNzMuNSA0MTAuNDM2IDEwMzkuNzdDMzExLjE4NiAxMDA2LjE4IDI3NS45MjcgODg3LjQwNSAyMDYuNjc3IDgwOC43MzdDMTM2LjcwMyA3MjkuMjQ4IDE0LjY1MzYgNjc5Ljk3OCAxLjIyNDE0IDU3NC45MDdDLTEyLjIxNDYgNDY5Ljc2MyA4OC4yODk2IDM4OC40MzMgMTQxLjIyMSAyOTYuNjEyQzE4OC42MTYgMjE0LjM5NCAyMDUuNjQzIDk0LjQ4MjEgMjk1LjU3NSA2NC4yODhDMzg4LjgwOSAzMi45ODUxIDQ3My4wNzEgMTYyLjM1MSA1NzAuOTA1IDE1Mi4zNDhaJyBmaWxsPScjQTQ5NUZCJy8+PC9zdmc+Cg==)
}

.svg-mask--splat-2 img,
.svg-mask--splat-2 svg:not(.icon),
.svg-mask--splat-2 video {
    -webkit-mask-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSdub25lJyBoZWlnaHQ9Jzg5Nicgdmlld0JveD0nMCAwIDk3NyA4OTYnIHdpZHRoPSc5NzcnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggY2xpcC1ydWxlPSdldmVub2RkJyBkPSdtOTc1LjY4NCAzODEuODMyYzE3LjI2MyA5NS4wMzMtMTQwLjIxNCAxMjkuMjMxLTE4Ny4xNzcgMjEzLjY2Ni01Mi4xMzggOTMuNzM3IDguNTE1IDI4OC4wOTUtOTguMjUzIDI5OS41MDMtMTI3LjE3NSAxMy41ODctMTM4LjA0OS0xMTUuMzYyLTI2My0xNDIuNjMtMTA5LjU4NS0yMy45MTQtMjI0LjUzNSA5MS44OTQtMzA4LjcxNSAxNy44NjQtODQuNTQzMy03NC4zNDggMTE4LjY4OC0yMDcuMzczIDkzLjgyOS0zMTcuMDk0LTIyLjE0OS05Ny43Ni0yMDQuMTk2NzItMTA0Ljg3OC0yMTIuMDcxNDAxLTIwNC44LTYuODY1NTc5LTg3LjExOCAxMDcuMDIxNDAxLTEzNi4xNzcgMTgxLjU5NjQwMS0xODEuOTAxMyA2Ni4xNjMtNDAuNTY2OCAxNDIuNTMxLTc2LjcwNDkgMjE5LjA4Mi02My43Njk5NiA3MS4zODMgMTIuMDYxNzYgMTQwLjQ4NiA2Mi43NzM1NiAxNjguODUyIDEyOS4zMjAyNiA5LjkyOSAyMy4yOTMgMjUuODM5IDU0LjUyOCA2OS45MiA3OC41MTcgOTEuODU5IDQ5Ljk5IDMwNC44MTYgMCAzMzUuOTM3IDE3MS4zMjV6JyBmaWxsPScjYTQ5NWZiJyBmaWxsLXJ1bGU9J2V2ZW5vZGQnLz48L3N2Zz4=);
    mask-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSdub25lJyBoZWlnaHQ9Jzg5Nicgdmlld0JveD0nMCAwIDk3NyA4OTYnIHdpZHRoPSc5NzcnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggY2xpcC1ydWxlPSdldmVub2RkJyBkPSdtOTc1LjY4NCAzODEuODMyYzE3LjI2MyA5NS4wMzMtMTQwLjIxNCAxMjkuMjMxLTE4Ny4xNzcgMjEzLjY2Ni01Mi4xMzggOTMuNzM3IDguNTE1IDI4OC4wOTUtOTguMjUzIDI5OS41MDMtMTI3LjE3NSAxMy41ODctMTM4LjA0OS0xMTUuMzYyLTI2My0xNDIuNjMtMTA5LjU4NS0yMy45MTQtMjI0LjUzNSA5MS44OTQtMzA4LjcxNSAxNy44NjQtODQuNTQzMy03NC4zNDggMTE4LjY4OC0yMDcuMzczIDkzLjgyOS0zMTcuMDk0LTIyLjE0OS05Ny43Ni0yMDQuMTk2NzItMTA0Ljg3OC0yMTIuMDcxNDAxLTIwNC44LTYuODY1NTc5LTg3LjExOCAxMDcuMDIxNDAxLTEzNi4xNzcgMTgxLjU5NjQwMS0xODEuOTAxMyA2Ni4xNjMtNDAuNTY2OCAxNDIuNTMxLTc2LjcwNDkgMjE5LjA4Mi02My43Njk5NiA3MS4zODMgMTIuMDYxNzYgMTQwLjQ4NiA2Mi43NzM1NiAxNjguODUyIDEyOS4zMjAyNiA5LjkyOSAyMy4yOTMgMjUuODM5IDU0LjUyOCA2OS45MiA3OC41MTcgOTEuODU5IDQ5Ljk5IDMwNC44MTYgMCAzMzUuOTM3IDE3MS4zMjV6JyBmaWxsPScjYTQ5NWZiJyBmaWxsLXJ1bGU9J2V2ZW5vZGQnLz48L3N2Zz4=)
}

.svg-mask--splat-3 img,
.svg-mask--splat-3 svg:not(.icon),
.svg-mask--splat-3 video {
    -webkit-mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTA3MicgaGVpZ2h0PScxMDUxJyB2aWV3Qm94PScwIDAgMTA3MiAxMDUxJyBmaWxsPSdub25lJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnPjxwYXRoIGZpbGwtcnVsZT0nZXZlbm9kZCcgY2xpcC1ydWxlPSdldmVub2RkJyBkPSdNNjE4LjkxMyAyMTUuMzgzQzc0NS43NzUgMjI1LjY1NyA4NDEuNzUgMTguMDA0OSA5NjAuNjc4IDYzLjMyMjRDMTA2OS44NSAxMDQuOTI0IDEwNzAuNDMgMjcxLjI2MiAxMDcxLjk5IDM4OC4wMjhDMTA3My4yNSA0ODIuMjg4IDk3Mi4wMjYgNTUwLjg1NSA5NjguNDQ5IDY0NS4wNTVDOTY0LjE0NyA3NTguMzMxIDExMDMuNjkgODY4LjI5OCAxMDQ5Ljk2IDk2OC4xMjZDMTAwMC4yNCAxMDYwLjUgODU2LjQ2OCAxMDM4LjgyIDc1MS44NzYgMTA0Ny40MUM2NjQuMzE2IDEwNTQuNiA1NzMuNjY0IDEwNTQuNDQgNDk1Ljg3IDEwMTMuNjRDNDI0LjczMSA5NzYuMzMxIDQwOS44MzQgODc4LjY4OSAzNDMuNTgyIDgzMy4yNzJDMjQxLjM0OSA3NjMuMTkgNjYuMzk5NyA3OTMuNDE2IDEyLjExOTQgNjgyLjAyM0MtMzcuNTczOCA1ODAuMDQ0IDc5LjI3NTYgNDcyLjUzOCAxMTUuNDAyIDM2NS4wMDhDMTU2LjY3MiAyNDIuMTY0IDExNS4zMiA0MS44NTYzIDIzOS41OTggNC45MjU3M0MzODEuMDgxIC0zNy4xMTc2IDQ3MS43OTEgMjAzLjQ2OCA2MTguOTEzIDIxNS4zODNaJyBmaWxsPScjQTQ5NUZCJy8+PC9zdmc+Cg==);
    mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTA3MicgaGVpZ2h0PScxMDUxJyB2aWV3Qm94PScwIDAgMTA3MiAxMDUxJyBmaWxsPSdub25lJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnPjxwYXRoIGZpbGwtcnVsZT0nZXZlbm9kZCcgY2xpcC1ydWxlPSdldmVub2RkJyBkPSdNNjE4LjkxMyAyMTUuMzgzQzc0NS43NzUgMjI1LjY1NyA4NDEuNzUgMTguMDA0OSA5NjAuNjc4IDYzLjMyMjRDMTA2OS44NSAxMDQuOTI0IDEwNzAuNDMgMjcxLjI2MiAxMDcxLjk5IDM4OC4wMjhDMTA3My4yNSA0ODIuMjg4IDk3Mi4wMjYgNTUwLjg1NSA5NjguNDQ5IDY0NS4wNTVDOTY0LjE0NyA3NTguMzMxIDExMDMuNjkgODY4LjI5OCAxMDQ5Ljk2IDk2OC4xMjZDMTAwMC4yNCAxMDYwLjUgODU2LjQ2OCAxMDM4LjgyIDc1MS44NzYgMTA0Ny40MUM2NjQuMzE2IDEwNTQuNiA1NzMuNjY0IDEwNTQuNDQgNDk1Ljg3IDEwMTMuNjRDNDI0LjczMSA5NzYuMzMxIDQwOS44MzQgODc4LjY4OSAzNDMuNTgyIDgzMy4yNzJDMjQxLjM0OSA3NjMuMTkgNjYuMzk5NyA3OTMuNDE2IDEyLjExOTQgNjgyLjAyM0MtMzcuNTczOCA1ODAuMDQ0IDc5LjI3NTYgNDcyLjUzOCAxMTUuNDAyIDM2NS4wMDhDMTU2LjY3MiAyNDIuMTY0IDExNS4zMiA0MS44NTYzIDIzOS41OTggNC45MjU3M0MzODEuMDgxIC0zNy4xMTc2IDQ3MS43OTEgMjAzLjQ2OCA2MTguOTEzIDIxNS4zODNaJyBmaWxsPScjQTQ5NUZCJy8+PC9zdmc+Cg==)
}

.svg-mask--splat-4 img,
.svg-mask--splat-4 svg:not(.icon),
.svg-mask--splat-4 video {
    -webkit-mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNTE4JyBoZWlnaHQ9JzUwNCcgdmlld0JveD0nMCAwIDUxOCA1MDQnIGZpbGw9J25vbmUnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZmlsbC1ydWxlPSdldmVub2RkJyBjbGlwLXJ1bGU9J2V2ZW5vZGQnIGQ9J00zMDMuNzkzIDg0LjY3M0MzMTkuOTQyIDEwOC41MjEgMzM1LjUzIDEyOC4yNTMgMzYwLjI4OSAxMzMuMzk3QzQwMy42NDMgMTQyLjQwNCA0NTQuNDIxIDk3LjczMiA0OTAuNDc5IDEyMy40NTdDNTIwLjMxMSAxNDQuNzM5IDUyMy44IDIxMC42NDYgNTEwLjQ3OSAyMzUuOTk5QzQ5NC45NzkgMjY1LjQ5OSA0NTguMjc4IDI4MC4wNCA0NDIuOTc5IDMwOS45OTlDNDI0LjYyOSAzNDUuOTMxIDQ1My43NCAzODQuOTY3IDQxMC40NzkgNDEzLjQ5OUMzNzcuNTc4IDQzNS4xOTggMzQxLjgxNiAzOTcuODcyIDMwMy43OTMgNDA4LjE5OEMyNDMuODg2IDQyNC40NjYgMjA5LjIgNTA0LjU0MSAxNDcuMTQ4IDUwMi45OTlDODkuMzQxNCA1MDEuNTYzIDMxLjUxMDMgNDU0LjgzNiAxMC40NzkzIDQwMC45MDhDLTEwLjc0ODEgMzQ2LjQ3NyAyLjQzNTM5IDI3NS4wNTYgNDEuMjE1IDIzMS4zNjZDNjkuNSAxOTkuNSAxMDUgMTg5IDEyMi40OCAxMzlDMTMxLjkzNSAxMTEuOTUzIDExOS40OCAzNiAxNzQuMzc3IDYuOTQ5OThDMjE5LjYxNyAtMTYuOTg5OSAyNjYuNDQyIDI0LjAyMjEgMzAzLjc5MyA4NC42NzNaJyBmaWxsPScjQTQ5NUZCJy8+PC9zdmc+Cg==);
    mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNTE4JyBoZWlnaHQ9JzUwNCcgdmlld0JveD0nMCAwIDUxOCA1MDQnIGZpbGw9J25vbmUnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZmlsbC1ydWxlPSdldmVub2RkJyBjbGlwLXJ1bGU9J2V2ZW5vZGQnIGQ9J00zMDMuNzkzIDg0LjY3M0MzMTkuOTQyIDEwOC41MjEgMzM1LjUzIDEyOC4yNTMgMzYwLjI4OSAxMzMuMzk3QzQwMy42NDMgMTQyLjQwNCA0NTQuNDIxIDk3LjczMiA0OTAuNDc5IDEyMy40NTdDNTIwLjMxMSAxNDQuNzM5IDUyMy44IDIxMC42NDYgNTEwLjQ3OSAyMzUuOTk5QzQ5NC45NzkgMjY1LjQ5OSA0NTguMjc4IDI4MC4wNCA0NDIuOTc5IDMwOS45OTlDNDI0LjYyOSAzNDUuOTMxIDQ1My43NCAzODQuOTY3IDQxMC40NzkgNDEzLjQ5OUMzNzcuNTc4IDQzNS4xOTggMzQxLjgxNiAzOTcuODcyIDMwMy43OTMgNDA4LjE5OEMyNDMuODg2IDQyNC40NjYgMjA5LjIgNTA0LjU0MSAxNDcuMTQ4IDUwMi45OTlDODkuMzQxNCA1MDEuNTYzIDMxLjUxMDMgNDU0LjgzNiAxMC40NzkzIDQwMC45MDhDLTEwLjc0ODEgMzQ2LjQ3NyAyLjQzNTM5IDI3NS4wNTYgNDEuMjE1IDIzMS4zNjZDNjkuNSAxOTkuNSAxMDUgMTg5IDEyMi40OCAxMzlDMTMxLjkzNSAxMTEuOTUzIDExOS40OCAzNiAxNzQuMzc3IDYuOTQ5OThDMjE5LjYxNyAtMTYuOTg5OSAyNjYuNDQyIDI0LjAyMjEgMzAzLjc5MyA4NC42NzNaJyBmaWxsPScjQTQ5NUZCJy8+PC9zdmc+Cg==)
}

.background-svg--wave {
    position: relative;
    padding-bottom: 100px !important;
    border-bottom: 0 !important
}

@media only screen and (max-width:768px) {
    .background-svg--wave {
        padding-bottom: 90px !important
    }
}

.background-svg--wave:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 85px;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: var(--color-body);
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 1439 57' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 57L1439 57V-3.8147e-06C1400.62 -3.8147e-06 1313.56 90.4365 1126.12 20.9833C938.69 -48.4699 829.284 116.882 444.121 20.9833C220.961 -34.5797 28.1238 37.491 0 57Z' fill='currentColor'/%3E%3C/svg%3E%0A");
    mask-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 1439 57' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 57L1439 57V-3.8147e-06C1400.62 -3.8147e-06 1313.56 90.4365 1126.12 20.9833C938.69 -48.4699 829.284 116.882 444.121 20.9833C220.961 -34.5797 28.1238 37.491 0 57Z' fill='currentColor'/%3E%3C/svg%3E%0A");
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: cover;
    mask-size: cover
}

@media only screen and (max-width:768px) {
    .background-svg--wave:after {
        -webkit-mask-size: 190%;
        mask-size: 190%;
        -webkit-mask-position: left bottom;
        mask-position: left bottom
    }
}

.background-svg--wave-reverse {
    position: relative;
    padding-top: 100px !important;
    border-top: 0 !important
}

@media only screen and (max-width:768px) {
    .background-svg--wave-reverse {
        padding-top: 90px !important
    }
}

.background-svg--wave-reverse:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 85px;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: var(--color-body);
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 1439 57' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 57L1439 57V-3.8147e-06C1400.62 -3.8147e-06 1313.56 90.4365 1126.12 20.9833C938.69 -48.4699 829.284 116.882 444.121 20.9833C220.961 -34.5797 28.1238 37.491 0 57Z' fill='currentColor'/%3E%3C/svg%3E%0A");
    mask-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 1439 57' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 57L1439 57V-3.8147e-06C1400.62 -3.8147e-06 1313.56 90.4365 1126.12 20.9833C938.69 -48.4699 829.284 116.882 444.121 20.9833C220.961 -34.5797 28.1238 37.491 0 57Z' fill='currentColor'/%3E%3C/svg%3E%0A");
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: cover;
    mask-size: cover;
    transform: rotate(180deg)
}

@media only screen and (max-width:768px) {
    .background-svg--wave-reverse:after {
        -webkit-mask-size: 190%;
        mask-size: 190%;
        -webkit-mask-position: left bottom;
        mask-position: left bottom
    }
}

.rte {
    display: block;
    margin-bottom: calc(var(--gutter) / 4)
}

@media only screen and (min-width:769px) {
    .rte {
        margin-bottom: calc(var(--gutter) / 2)
    }
}

.rte:last-child {
    margin-bottom: 0
}

.rte+.rte {
    margin-top: var(--gutter)
}

.rte p,
.rte ul,
.rte ol,
.rte table {
    margin-bottom: 15px
}

@media only screen and (min-width:769px) {

    .rte p,
    .rte ul,
    .rte ol,
    .rte table {
        margin-bottom: 25px
    }
}

.rte p:last-child,
.rte ul:last-child,
.rte ol:last-child,
.rte table:last-child {
    margin-bottom: 0
}

.rte ul ul {
    margin-bottom: 0
}

.rte h1,
.rte h2,
.rte h3,
.rte h4,
.rte h5,
.rte h6 {
    margin-top: 60px;
    margin-bottom: 25px
}

.rte h1:first-child,
.rte h2:first-child,
.rte h3:first-child,
.rte h4:first-child,
.rte h5:first-child,
.rte h6:first-child {
    margin-top: 0
}

.rte h1 a,
.rte h2 a,
.rte h3 a,
.rte h4 a,
.rte h5 a,
.rte h6 a {
    -webkit-text-decoration: none;
    text-decoration: none
}

.rte meta:first-child+h1,
.rte meta:first-child+h2,
.rte meta:first-child+h3,
.rte meta:first-child+h4,
.rte meta:first-child+h5,
.rte meta:first-child+h6 {
    margin-top: 0
}

.rte>div {
    margin-bottom: calc(var(--gutter) / 2)
}

.rte>div:last-child {
    margin-bottom: 0
}

.rte li {
    margin-bottom: 0
}

.rte table {
    table-layout: fixed
}

.rte--block {
    margin-bottom: 8px
}

@media only screen and (min-width:769px) {
    .rte--block {
        margin-bottom: 12px
    }
}

.rte-setting>p:last-child {
    margin-bottom: 0
}

.rte a,
.rte-setting a {
    -webkit-text-decoration: none;
    text-decoration: none
}

.rte img,
.rte-setting img {
    height: auto
}

.rte a:not(.rte__image):not(.btn),
.rte-setting a:not(.rte__image):not(.btn) {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.rte a.rte__image:after,
.rte-setting a.rte__image:after {
    content: none
}

.text-center .rte ul,
.text-center.rte ul,
.text-center .rte ol,
.text-center.rte ol {
    list-style-position: inside;
    margin-left: 0
}

.rte--nomargin {
    margin-bottom: 0
}

.float-grid {
    list-style: none;
    margin: 0;
    padding: 0;
    margin-left: calc(var(--grid-gutter) * -1)
}

@media only screen and (max-width:768px) {
    .float-grid {
        margin-left: calc(var(--grid-gutter-small) * -1)
    }

    html[dir='rtl'] .float-grid {
        margin-left: 0;
        margin-right: calc(var(--grid-gutter-small) * -1)
    }
}

html[dir='rtl'] .float-grid {
    margin-left: 0;
    margin-right: calc(var(--grid-gutter) * -1)
}

.grid--small {
    margin-left: -10px
}

.grid--small .grid__item {
    padding-left: 10px
}

.grid__item {
    float: left;
    padding-left: var(--grid-gutter);
    width: 100%;
    min-height: 1px
}

@media only screen and (max-width:768px) {
    .grid__item {
        padding-left: var(--grid-gutter-small)
    }

    html[dir='rtl'] .grid__item {
        padding-left: 0;
        padding-right: var(--grid-gutter-small)
    }
}

html[dir='rtl'] .grid__item {
    float: right;
    padding-left: 0;
    padding-right: var(--grid-gutter)
}

.grid--flush-bottom {
    margin-bottom: calc(var(--grid-gutter) * -1);
    overflow: auto
}

.grid--flush-bottom>.grid__item {
    margin-bottom: var(--grid-gutter)
}

.grid--center {
    text-align: center
}

.grid--center .grid__item {
    float: none;
    display: inline-block;
    vertical-align: top;
    text-align: left
}

html[dir='rtl'] .grid--center .grid__item {
    text-align: right
}

.new-grid {
    display: flex;
    flex-wrap: wrap;
    margin-left: calc(var(--product-grid-margin) * -1);
    margin-right: calc(var(--product-grid-margin) * -1);
    word-break: break-word
}

.new-grid--center {
    justify-content: center
}

.grid-item {
    flex: 0 0 100%;
    align-items: stretch;
    display: flex;
    margin-bottom: var(--product-grid-margin);
    padding-left: var(--product-grid-margin);
    padding-right: var(--product-grid-margin)
}

[data-view='custom-grid-item-width'] .grid-item.medium-up--one-sixth {
    flex: 0 auto
}

[data-view='custom-grid-item-width'] .grid-item {
    flex: auto
}

[data-view='scrollable'] .grid-item {
    flex: 0 0 calc(100% / 6);
    max-width: 250px
}

[data-view='scrollable-7'] .grid-item {
    flex: 0 0 calc(100% / 7)
}

[data-view='scrollable-5'] .grid-item {
    flex: 0 0 calc(100% / 5)
}

[data-view='xsmall'] .grid-item {
    flex: 0 0 20%
}

[data-view='small'] .grid-item {
    flex: 0 0 25%
}

[data-view='medium'] .grid-item {
    flex: 0 0 calc(100% / 3)
}

[data-view='large'] .grid-item {
    flex: 0 0 50%
}

[data-view='6-3'] .grid-item {
    flex: 0 0 calc(100% / 6)
}

[data-view='6-2'] .grid-item {
    flex: 0 0 calc(100% / 6)
}

[data-view='3-1'] .grid-item {
    flex: 0 0 calc(100% / 3)
}

@media only screen and (max-width:768px) {
    [data-view='xsmall'] .grid-item {
        flex: 0 0 50%
    }

    [data-view='small'] .grid-item {
        flex: 0 0 50%
    }

    [data-view='medium'] .grid-item {
        flex: 0 0 50%
    }

    [data-view='large'] .grid-item {
        flex: 0 0 100%
    }

    [data-view='subcollections'] .grid-item {
        flex: 0 0 28%
    }

    [data-view='6-3'] .grid-item {
        flex: 0 0 calc(100% / 3)
    }

    [data-view='6-2'] .grid-item {
        flex: 0 0 50%
    }

    [data-view='3-1'] .grid-item {
        flex: 0 0 100%
    }
}

.grid-item__content {
    position: relative;
    display: flex;
    flex-direction: column;
    text-align: left;
    width: 100%;
    background-color: var(--color-body)
}

html[dir='rtl'] .grid-item__content {
    text-align: right
}

.grid-item__link {
    display: block;
    width: 100%
}

[data-grid-style='grey-round'] .grid-item__link {
    overflow: hidden;
    border-radius: var(--product-radius) var(--product-radius) 0 0
}

[data-grid-style='white-round'] .grid-item__link {
    overflow: hidden;
    border-radius: var(--product-radius)
}

[data-grid-style='grey-round'] [data-view='list'] .grid-item__link {
    border-radius: var(--product-radius) 0 0 var(--product-radius)
}

[data-grid-style='white-round'] [data-view='list'] .grid-item__link {
    border-radius: var(--product-radius) 0 0 var(--product-radius)
}

.grid-item__meta {
    margin: 12px
}

@media only screen and (min-width:769px) {
    .grid-item__meta {
        margin: 20px
    }
}

.scrollable-grid {
    overflow: hidden;
    overflow-x: auto;
    flex-wrap: nowrap;
    margin: -20px -10px 0 0;
    padding: 20px 10px 0 var(--grid-thickness)
}

.scrollable-grid .grid-item {
    flex: 0 0 25%
}

.site-header__cart .scrollable-grid .grid-item {
    min-width: 150px
}

.new-grid[data-type='subcollections'] {
    margin-bottom: 20px;
    padding-bottom: 5px
}

.new-grid[data-type='subcollections']:empty {
    border: 0;
    margin-bottom: 0;
    padding-bottom: 0
}

@media only screen and (max-width:768px) {
    .scrollable-grid--small {
        overflow: hidden;
        overflow-x: auto;
        flex-wrap: nowrap;
        justify-content: flex-start;
        margin: calc(var(--product-grid-margin) * -1) calc(var(--page-width-gutter-small) * -1) 0 0;
        padding: var(--product-grid-margin) var(--page-width-gutter-small) 0 0
    }

    .page-width--flush-small .scrollable-grid--small {
        margin-left: 0;
        margin-right: 0;
        padding-left: var(--page-width-gutter-small)
    }

    .scrollable-grid--small .grid-item {
        flex: 0 0 45%
    }

    .scrollable-grid--small[data-type='subcollections'] {
        margin-left: calc(var(--grid-gutter-small) * -1);
        margin-bottom: 0;
        padding-bottom: 10px
    }

    .scrollable-grid--small[data-type='subcollections'] .grid-item {
        flex: 0 0 30%
    }

    [data-view*='scrollable']:after {
        content: '';
        display: block;
        height: 1px;
        min-width: var(--page-width-padding)
    }
}

.one-whole {
    width: 100%
}

.one-half {
    width: 50%
}

.one-third {
    width: 33.33333%
}

.two-thirds {
    width: 66.66667%
}

.one-quarter {
    width: 25%
}

.two-quarters {
    width: 50%
}

.three-quarters {
    width: 75%
}

.one-fifth {
    width: 20%
}

.two-fifths {
    width: 40%
}

.three-fifths {
    width: 60%
}

.four-fifths {
    width: 80%
}

.one-sixth {
    width: 16.66667%
}

.two-sixths {
    width: 33.33333%
}

.three-sixths {
    width: 50%
}

.four-sixths {
    width: 66.66667%
}

.five-sixths {
    width: 83.33333%
}

@media only screen and (max-width:768px) {
    .small--one-whole {
        width: 100%
    }

    .small--one-half {
        width: 50%
    }

    .small--one-third {
        width: 33.33333%
    }

    .small--two-thirds {
        width: 66.66667%
    }

    .grid--uniform .small--one-half:nth-of-type(odd),
    .grid--uniform .small--one-third:nth-of-type(3n+1) {
        clear: both
    }

    .small--one-quarter {
        width: 25%
    }

    .small--two-quarters {
        width: 50%
    }

    .small--three-quarters {
        width: 75%
    }

    .grid--uniform .small--one-quarter:nth-of-type(4n+1) {
        clear: both
    }

    .small--one-fifth {
        width: 20%
    }

    .small--two-fifths {
        width: 40%
    }

    .small--three-fifths {
        width: 60%
    }

    .small--four-fifths {
        width: 80%
    }

    .grid--uniform .small--one-fifth:nth-of-type(5n+1) {
        clear: both
    }

    .small--one-sixth {
        width: 16.66667%
    }

    .small--two-sixths {
        width: 33.33333%
    }

    .small--three-sixths {
        width: 50%
    }

    .small--four-sixths {
        width: 66.66667%
    }

    .small--five-sixths {
        width: 83.33333%
    }

    .grid--uniform .small--one-sixth:nth-of-type(6n+1),
    .grid--uniform .small--three-sixths:nth-of-type(odd),
    .grid--uniform .small--two-sixths:nth-of-type(3n+1) {
        clear: both
    }
}

@media only screen and (min-width:769px) {
    .medium-up--one-whole {
        width: 100%
    }

    .medium-up--one-half {
        width: 50%
    }

    .medium-up--one-third {
        width: 33.33333%
    }

    .medium-up--two-thirds {
        width: 66.66667%
    }

    .grid--uniform .medium-up--one-half:nth-of-type(odd),
    .grid--uniform .medium-up--one-third:nth-of-type(3n+1) {
        clear: both
    }

    .medium-up--one-quarter {
        width: 25%
    }

    .medium-up--two-quarters {
        width: 50%
    }

    .medium-up--three-quarters {
        width: 75%
    }

    .grid--uniform .medium-up--one-quarter:nth-of-type(4n+1) {
        clear: both
    }

    .medium-up--one-fifth {
        width: 20%
    }

    .medium-up--two-fifths {
        width: 40%
    }

    .medium-up--three-fifths {
        width: 60%
    }

    .medium-up--four-fifths {
        width: 80%
    }

    .grid--uniform .medium-up--one-fifth:nth-of-type(5n+1) {
        clear: both
    }

    .medium-up--one-sixth {
        width: 16.66667%
    }

    .medium-up--two-sixths {
        width: 33.33333%
    }

    .medium-up--three-sixths {
        width: 50%
    }

    .medium-up--four-sixths {
        width: 66.66667%
    }

    .medium-up--five-sixths {
        width: 83.33333%
    }

    .grid--uniform .medium-up--one-sixth:nth-of-type(6n+1),
    .grid--uniform .medium-up--three-sixths:nth-of-type(odd),
    .grid--uniform .medium-up--two-sixths:nth-of-type(3n+1) {
        clear: both
    }
}

button {
    overflow: visible;
    color: currentColor
}

button[disabled],
html input[disabled] {
    cursor: default
}

.btn,
.rte .btn,
.shopify-payment-button .shopify-payment-button__button--unbranded {
    line-height: 1.42;
    -webkit-text-decoration: none;
    text-decoration: none;
    text-align: center;
    white-space: normal;
    font-size: calc(var(--type-base-size) + 2px);
    font-weight: var(--type-header-weight);
    display: inline-block;
    padding: var(--button-padding);
    margin: 0;
    width: auto;
    min-width: 90px;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid #fff0;
    -webkit-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    border-radius: var(--button-radius);
    color: var(--color-button-primary-text);
    background: var(--color-button-primary)
}

.btn:hover,
.rte .btn:hover,
.shopify-payment-button .shopify-payment-button__button--unbranded:hover {
    color: var(--color-button-primary-text);
    background-color: var(--color-button-primary)
}

.btn[disabled],
.rte .btn[disabled],
.shopify-payment-button .shopify-payment-button__button--unbranded[disabled],
.btn.disabled,
.rte .btn.disabled,
.shopify-payment-button .shopify-payment-button__button--unbranded.disabled {
    cursor: default;
    color: var(--disabled-grey-text);
    background-color: var(--disabled-grey)
}

.btn[disabled]:hover,
.rte .btn[disabled]:hover,
.shopify-payment-button .shopify-payment-button__button--unbranded[disabled]:hover,
.btn.disabled:hover,
.rte .btn.disabled:hover,
.shopify-payment-button .shopify-payment-button__button--unbranded.disabled:hover {
    color: var(--disabled-grey-text);
    background-color: var(--disabled-grey)
}

.shopify-payment-button .shopify-payment-button__button--unbranded:hover:not([disabled]) {
    color: var(--color-button-primary-text);
    background-color: var(--color-button-primary)
}

.shopify-payment-button .shopify-payment-button__button--unbranded {
    height: auto
}

.shopify-payment-button__more-options {
    color: inherit
}

.btn--secondary,
.block-buy-buttons[data-show-dynamic-checkout] .btn,
.rte .btn--secondary {
    border: 1px solid;
    border-color: var(--color-text-body);
    color: var(--color-text-body);
    background-color: var(--color-body)
}

.btn--secondary:hover,
.block-buy-buttons[data-show-dynamic-checkout] .btn:hover,
.rte .btn--secondary:hover {
    background-color: var(--color-body);
    color: var(--color-text-body)
}

.btn--secondary[disabled],
.block-buy-buttons[data-show-dynamic-checkout] .btn[disabled],
.rte .btn--secondary[disabled],
.btn--secondary.disabled,
.block-buy-buttons[data-show-dynamic-checkout] .btn.disabled,
.rte .btn--secondary.disabled {
    cursor: default;
    color: var(--disabled-grey-text);
    background-color: var(--disabled-grey);
    border-color: var(--disabled-border)
}

.btn--circle {
    padding: 8px;
    border-radius: 50%;
    min-width: 0;
    line-height: 1
}

.btn--circle .icon {
    width: 18px;
    height: 18px
}

.btn--circle:before,
.btn--circle:after {
    content: none;
    background: none;
    width: auto
}

.btn--circle.btn--large .icon {
    width: 30px;
    height: 30px
}

.btn--circle.btn--large {
    padding: 15px
}

.btn--circle.btn--loading {
    text-indent: unset
}

@media only screen and (max-width:768px) {
    .btn--circle.btn--loading:before {
        width: 18px;
        height: 18px;
        margin-left: -9px;
        margin-top: -9px;
        border-width: 2px
    }
}

.btn--circle.btn--loading svg {
    opacity: 0
}

.btn--icon {
    min-width: 0;
    padding: 6px
}

.btn--icon .icon {
    display: block;
    width: 23px;
    height: 23px
}

.btn--small {
    padding: 8px 14px;
    font-size: calc(var(--type-base-size) - 2px)
}

.btn--large {
    padding: 15px 20px
}

.btn--full {
    width: 100%
}

.btn--inverse {
    background-color: #fff0;
    color: #fff;
    border: 2px solid #fff
}

.btn--inverse:hover,
.btn--inverse:focus {
    background-color: #fff0
}

.btn--loading {
    position: relative;
    text-indent: -9999px;
    background-color: var(--color-button-primary-dim);
    color: var(--color-button-primary-dim)
}

.btn--loading:hover,
.btn--loading:active {
    background-color: var(--color-button-primary-dim);
    color: var(--color-button-primary-dim)
}

.btn--loading:before {
    content: '';
    display: block;
    width: 22px;
    height: 22px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -11px;
    margin-top: -11px;
    border-radius: 50%;
    border: 3px solid;
    border-color: var(--color-button-primary-text);
    border-top-color: #fff0;
    animation: spin 1s infinite linear
}

.btn--loading.btn--secondary,
.block-buy-buttons[data-show-dynamic-checkout] .btn--loading {
    color: var(--color-text-body);
    background: #fff0
}

.btn--loading.btn--secondary:before,
.block-buy-buttons[data-show-dynamic-checkout] .btn--loading:before {
    border-color: var(--color-text-body);
    border-top-color: #fff0
}

.collapsible-trigger-btn {
    text-align: left;
    display: block;
    width: 100%;
    padding: 15px 0;
    font-weight: var(--type-header-weight);
    font-size: calc(var(--type-base-size) + 3px)
}

@media only screen and (max-width:768px) {
    .collapsible-trigger-btn {
        padding: 12px 17px 12px 0;
        font-size: calc(var(--type-base-size) + 1px)
    }
}

.collapsible-content__inner {
    padding: 0 0 15px
}

@media only screen and (max-width:768px) {
    .collapsible-content__inner {
        padding: 0 17px 12px
    }
}

.collapsible-trigger-btn--borders {
    border-bottom: 1px solid;
    border-color: var(--color-border)
}

.collapsible-trigger-btn--borders.is-open {
    border-color: #fff0
}

.collapsible-content+.collapsible-trigger-btn--borders {
    margin-top: -1px
}

.collapsible-trigger-btn--borders+.collapsible-content--expanded {
    margin-bottom: var(--gutter)
}

.collapsible-trigger-btn--borders+.collapsible-content--expanded:last-child {
    margin-bottom: -1px
}

.collapsible-trigger-btn--borders-top {
    border-top: 1px solid;
    border-top-color: var(--color-border)
}

.shopify-payment-button {
    margin-top: 10px
}

.shopify-payment-button .shopify-payment-button__button--unbranded {
    display: block;
    width: 100%;
    transition: none
}

.payment-buttons .cart__checkout,
.payment-buttons .add-to-cart,
.payment-buttons .shopify-payment-button,
.payment-buttons .shopify-payment-button__button--unbranded {
    min-height: 54px
}

.add-to-cart.btn--secondary {
    border: 1px solid;
    border-color: var(--color-text-body)
}

.add-to-cart.btn--secondary.disabled,
.add-to-cart.btn--secondary[disabled] {
    border-color: var(--disabled-border)
}

.shopify-payment-button__button--hidden {
    display: none !important
}

form {
    margin: 0
}

.form-vertical {
    margin-bottom: calc(var(--gutter) / 2)
}

.form-vertical label {
    text-align: left
}

[dir='rtl'] .form-vertical label {
    text-align: right
}

button,
input,
textarea {
    -webkit-appearance: none;
    -moz-appearance: none
}

button {
    background: none;
    border: none;
    display: inline-block;
    cursor: pointer
}

fieldset {
    border: 1px solid;
    border-color: var(--color-border);
    padding: calc(var(--gutter) / 2)
}

legend {
    border: 0;
    padding: 0
}

button,
input[type='submit'] {
    cursor: pointer
}

input,
textarea,
select {
    border: 1px solid;
    border-color: var(--color-border);
    max-width: 100%;
    padding: 8px 10px;
    border-radius: var(--input-radius)
}

input[disabled],
textarea[disabled],
select[disabled],
input.disabled,
textarea.disabled,
select.disabled {
    cursor: default;
    background-color: var(--disabled-grey);
    border-color: var(--disabled-border)
}

input.input-full,
textarea.input-full,
select.input-full {
    width: 100%
}

textarea {
    min-height: 100px
}

input[type='checkbox'],
input[type='radio'] {
    margin: 0 10px 0 0;
    padding: 0;
    width: auto
}

input[type='checkbox'] {
    -webkit-appearance: checkbox;
    -moz-appearance: checkbox
}

input[type='radio'] {
    -webkit-appearance: radio;
    -moz-appearance: radio
}

input[type='image'] {
    padding-left: 0;
    padding-right: 0
}

select,
.faux-select {
    -webkit-appearance: none;
    appearance: none;
    background-color: #fff;
    color: #000;
    padding: 8px 28px 8px 8px;
    text-indent: .01px;
    text-overflow: '';
    cursor: pointer;
    text-align: left;
    border: 1px solid;
    border-color: var(--color-border)
}

select {
    background-position: right center;
    background-image: var(--url-ico-select);
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 11px
}

.faux-select .icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 11px;
    height: 11px
}

option {
    color: #000;
    background-color: #fff
}

option[disabled] {
    color: #ccc
}

select::-ms-expand {
    display: none
}

label[for] {
    cursor: pointer
}

.form-vertical input,
.form-vertical select,
.form-vertical textarea {
    display: block;
    margin-bottom: 30px
}

.form-vertical input[type='checkbox'],
.form-vertical input[type='radio'],
.form-vertical .btn {
    display: inline-block
}

.form-vertical .btn:not(:last-child) {
    margin-bottom: 30px
}

small {
    display: block
}

input.error,
textarea.error {
    border-color: var(--error-red);
    background-color: var(--error-red-bg);
    color: var(--error-red)
}

label.error {
    color: var(--error-red)
}

.note,
.errors {
    border-radius: var(--input-radius);
    padding: 6px 12px;
    margin-bottom: calc(var(--gutter) / 2);
    border: 1px solid #fff0;
    text-align: left
}

.note ul,
.errors ul,
.note ol,
.errors ol {
    margin-top: 0;
    margin-bottom: 0
}

.note li:last-child,
.errors li:last-child {
    margin-bottom: 0
}

.note p,
.errors p {
    margin-bottom: 0
}

.note {
    border-color: var(--color-border)
}

.errors ul {
    list-style: disc outside;
    margin-left: 20px
}

.note--success {
    color: var(--success-green);
    background-color: var(--success-green-bg);
    border-color: var(--success-green)
}

.note--success a {
    color: var(--success-green);
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.note--success a:hover {
    -webkit-text-decoration: none;
    text-decoration: none
}

.form-error,
.errors {
    color: var(--error-red);
    background-color: var(--error-red-bg);
    border-color: var(--error-red)
}

.form-error a,
.errors a {
    color: var(--error-red);
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.form-error a:hover,
.errors a:hover {
    -webkit-text-decoration: none;
    text-decoration: none
}

::-webkit-input-placeholder {
    color: inherit;
    opacity: .5
}

:-moz-placeholder {
    color: inherit;
    opacity: .5
}

:-ms-input-placeholder {
    color: inherit;
    opacity: .5
}

::-ms-input-placeholder {
    color: inherit;
    opacity: 1
}

input,
textarea,
select {
    background-color: inherit;
    color: inherit
}

input[disabled],
textarea[disabled],
select[disabled],
input.disabled,
textarea.disabled,
select.disabled {
    background-color: var(--disabled-grey);
    border-color: #fff0
}

input:active,
textarea:active,
select:active,
input:focus,
textarea:focus,
select:focus {
    border: 1px solid;
    border-color: var(--color-text-body)
}

input[type='image'] {
    background-color: #fff0
}

a {
    color: var(--color-text-body);
    -webkit-text-decoration: none;
    text-decoration: none;
    background: #fff0
}

a:hover {
    color: var(--color-text-body)
}

.text-link {
    display: inline;
    border: 0 none;
    background: none;
    padding: 0;
    margin: 0;
    color: currentColor;
    background: #fff0
}

.text-link:hover {
    color: currentColor
}

.rte a,
.shopify-policy__container a,
.shopify-email-marketing-confirmation__container a {
    color: var(--color-link)
}

ul,
ol {
    margin: 0 0 calc(var(--gutter) / 2) var(--gutter);
    padding: 0;
    text-rendering: optimizeLegibility
}

ol ol {
    list-style: lower-alpha
}

ol {
    list-style: decimal
}

ul ul,
ul ol,
ol ol,
ol ul {
    margin: 4px 0 5px 20px
}

li {
    margin-bottom: .25em
}

ul.square {
    list-style: square outside
}

ul.disc {
    list-style: disc outside
}

ol.alpha {
    list-style: lower-alpha outside
}

.no-bullets {
    list-style: none outside;
    margin-left: 0
}

.inline-list {
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center
}

.inline-list li {
    display: block;
    margin-bottom: 0
}

.inline-list--no-wrap {
    flex-wrap: nowrap
}

table {
    width: 100%;
    border-spacing: 1px;
    position: relative;
    border: 0 none;
    background: var(--color-border)
}

.table-wrapper {
    max-width: 100%;
    overflow: auto;
    -webkit-overflow-scrolling: touch
}

td,
th {
    border: 0 none;
    text-align: left;
    padding: 10px 15px;
    background: var(--color-body)
}

html[dir='rtl'] td,
html[dir='rtl'] th {
    text-align: right
}

th,
.table__title {
    font-weight: var(--type-header-weight)
}

.flickity-enabled {
    position: relative
}

.flickity-enabled:focus {
    outline: none
}

.flickity-viewport {
    overflow: hidden;
    position: relative;
    transition: height 0.35s;
    height: 100%
}

.flickity-slider {
    position: absolute;
    width: 100%;
    height: 100%
}

.flickity-enabled.is-draggable {
    -webkit-user-select: none;
    user-select: none
}

.flickity-enabled.is-draggable .flickity-viewport {
    cursor: move;
    cursor: grab
}

.flickity-enabled.is-draggable .flickity-viewport.is-pointer-down {
    cursor: grabbing
}

.flickity-enabled.is-draggable[data-arrows='true'] .flickity-viewport {
    cursor: default
}

.flickity-enabled.is-draggable[data-arrows='true'] .flickity-viewport.is-pointer-down {
    cursor: default
}

.flickity-button {
    position: absolute;
    border: none;
    color: var(--color-button-primary-text);
    background: var(--color-button-primary);
    border-radius: 50%
}

.hero .flickity-button {
    color: var(--color-text-body);
    background-color: var(--color-body);
    box-shadow: 0 5px 5px rgb(0 0 0 / .1)
}

.flickity-button:hover {
    cursor: pointer;
    opacity: 1
}

.flickity-button:disabled {
    display: none;
    cursor: auto;
    pointer-events: none
}

.flickity-prev-next-button {
    top: 50%;
    width: 40px;
    height: 40px;
    transform: translateY(-50%)
}

@media only screen and (max-width:768px) {
    .flickity-prev-next-button {
        width: 33px;
        height: 33px
    }
}

.flickity-prev-next-button:hover {
    transform: translateY(-50%) scale(1.12)
}

.flickity-prev-next-button:active {
    transform: translateY(-50%) scale(1);
    transition: transform 0.05s ease-out
}

.flickity-previous {
    left: 10px
}

.flickity-next {
    right: 10px
}

.flickity-rtl .flickity-previous {
    left: auto;
    right: 10px
}

.flickity-rtl .flickity-next {
    right: auto;
    left: 10px
}

.flickity-button-icon {
    position: absolute;
    left: 35%;
    top: 35%;
    width: 30%;
    height: 30%;
    fill: currentColor
}

.flickity-page-dots {
    position: absolute;
    width: 100%;
    bottom: -25px;
    padding: 0;
    margin: 0;
    list-style: none;
    text-align: center;
    line-height: 1;
    color: currentColor;
    z-index: 2
}

.hero .flickity-page-dots {
    bottom: 20px;
    color: #fff
}

.flickity-rtl .flickity-page-dots {
    direction: rtl
}

.flickity-page-dots .dot {
    display: inline-block;
    vertical-align: middle;
    width: 6px;
    height: 6px;
    margin: 0 5px;
    border-radius: 100%;
    cursor: pointer;
    background-color: currentColor;
    opacity: .2
}

.flickity-page-dots .dot:hover {
    opacity: .6
}

.flickity-page-dots .dot.is-selected {
    opacity: 1;
    background-color: currentColor;
    width: 9px;
    height: 9px
}

.flickity-enabled.is-fade .flickity-slider>* {
    pointer-events: none;
    z-index: 0
}

.flickity-enabled.is-fade .flickity-slider>.is-selected {
    pointer-events: auto;
    z-index: 1
}

.hero[data-arrows='true'] {
    overflow: visible
}

.hero[data-arrows='true'] .flickity-prev-next-button {
    top: auto;
    bottom: -20px;
    transform: none;
    z-index: 2
}

.hero[data-arrows='true'] .flickity-prev-next-button:hover {
    transform: scale(1.12)
}

.hero[data-arrows='true'] .flickity-prev-next-button:active {
    transform: scale(1)
}

.hero[data-arrows='true'] .flickity-previous {
    left: auto;
    right: 90px
}

.hero[data-arrows='true'] .flickity-next {
    right: 40px
}

.hero.flickity-rtl[data-arrows='true'] .flickity-previous {
    right: auto;
    left: 90px
}

.hero.flickity-rtl[data-arrows='true'] .flickity-next {
    right: auto;
    left: 40px
}

@media only screen and (max-width:768px) {
    .hero[data-arrows='true'] .flickity-prev-next-button {
        bottom: -16px
    }

    .hero[data-arrows='true'] .flickity-previous {
        right: 60px
    }

    .hero[data-arrows='true'] .flickity-next {
        right: 20px
    }

    .hero.flickity-rtl[data-arrows='true'] .flickity-previous {
        left: 60px
    }

    .hero.flickity-rtl[data-arrows='true'] .flickity-next {
        left: 20px
    }
}

.hero[data-bars='true'] .flickity-page-dots {
    bottom: 0;
    height: 6px;
    line-height: 6px;
    z-index: 2
}

[data-bars='true'] .flickity-page-dots .dot {
    position: relative;
    border-radius: 0;
    width: 120px;
    height: 6px;
    border: 0;
    opacity: 1;
    vertical-align: top;
    background: none;
    overflow: hidden
}

@media only screen and (max-width:768px) {
    [data-bars='true'] .flickity-page-dots .dot {
        width: 45px
    }
}

[data-bars='true'] .flickity-page-dots .dot:before,
[data-bars='true'] .flickity-page-dots .dot:after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    height: 100%;
    width: 100%
}

[data-bars='true'] .flickity-page-dots .dot:before {
    opacity: .1;
    background-color: #000
}

[data-bars='true'] .flickity-page-dots .dot:hover:before {
    opacity: .2
}

[data-bars='true'] .flickity-page-dots .dot:after {
    transform: translateX(-100%);
    transition: none;
    background-color: currentColor
}

[data-bars='true'] .flickity-page-dots .dot.is-selected:after {
    animation: slideshowBars 0s linear forwards
}

.noUi-target,
.noUi-target * {
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: #fff0;
    -webkit-user-select: none;
    touch-action: none;
    user-select: none;
    box-sizing: border-box
}

.noUi-target {
    position: relative
}

.noUi-base,
.noUi-connects {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1
}

.noUi-connects {
    overflow: hidden;
    z-index: 0
}

.noUi-connect,
.noUi-origin {
    will-change: transform;
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;
    -ms-transform-origin: 0 0;
    -webkit-transform-origin: 0 0;
    -webkit-transform-style: preserve-3d;
    transform-origin: 0 0;
    transform-style: flat
}

.noUi-connect {
    height: 100%;
    width: 100%
}

.noUi-origin {
    height: 10%;
    width: 10%
}

.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {
    left: 0;
    right: auto
}

.noUi-vertical .noUi-origin {
    width: 0
}

.noUi-horizontal .noUi-origin {
    height: 0
}

.noUi-handle {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    position: absolute
}

.noUi-touch-area {
    height: 100%;
    width: 100%
}

.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
    transition: transform 0.3s
}

.noUi-state-drag * {
    cursor: inherit !important
}

.noUi-horizontal {
    height: 18px
}

.noUi-horizontal .noUi-handle {
    width: 34px;
    height: 28px;
    right: -17px;
    top: -6px
}

.noUi-vertical {
    width: 18px
}

.noUi-vertical .noUi-handle {
    width: 28px;
    height: 34px;
    right: -6px;
    top: -17px
}

.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {
    left: -17px;
    right: auto
}

.noUi-target {
    background: #fafafa;
    border-radius: 4px;
    border: 1px solid #d3d3d3;
    box-shadow: inset 0 1px 1px #f0f0f0, 0 3px 6px -5px #bbb
}

.noUi-connects {
    border-radius: 3px
}

.noUi-connect {
    background: #3fb8af
}

.noUi-draggable {
    cursor: ew-resize
}

.noUi-vertical .noUi-draggable {
    cursor: ns-resize
}

.noUi-handle {
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    background: #fff;
    cursor: default;
    box-shadow: inset 0 0 1px #fff, inset 0 1px 7px #ebebeb, 0 3px 6px -3px #bbb
}

.noUi-active {
    box-shadow: inset 0 0 1px #fff, inset 0 1px 7px #ddd, 0 3px 6px -3px #bbb
}

.noUi-handle:before,
.noUi-handle:after {
    content: '';
    display: block;
    position: absolute;
    height: 14px;
    width: 1px;
    background: #e8e7e6;
    left: 14px;
    top: 6px
}

.noUi-handle:after {
    left: 17px
}

.noUi-vertical .noUi-handle:before,
.noUi-vertical .noUi-handle:after {
    width: 14px;
    height: 1px;
    left: 6px;
    top: 14px
}

.noUi-vertical .noUi-handle:after {
    top: 17px
}

[disabled] .noUi-connect {
    background: #b8b8b8
}

[disabled].noUi-target,
[disabled].noUi-handle,
[disabled] .noUi-handle {
    cursor: not-allowed
}

.noUi-pips,
.noUi-pips * {
    box-sizing: border-box
}

.noUi-pips {
    position: absolute;
    color: #999
}

.noUi-value {
    position: absolute;
    white-space: nowrap;
    text-align: center
}

.noUi-value-sub {
    color: #ccc;
    font-size: 10px
}

.noUi-marker {
    position: absolute;
    background: #ccc
}

.noUi-marker-sub {
    background: #aaa
}

.noUi-marker-large {
    background: #aaa
}

.noUi-pips-horizontal {
    padding: 10px 0;
    height: 80px;
    top: 100%;
    left: 0;
    width: 100%
}

.noUi-value-horizontal {
    transform: translate(-50%, 50%)
}

.noUi-rtl .noUi-value-horizontal {
    transform: translate(50%, 50%)
}

.noUi-marker-horizontal.noUi-marker {
    margin-left: -1px;
    width: 2px;
    height: 5px
}

.noUi-marker-horizontal.noUi-marker-sub {
    height: 10px
}

.noUi-marker-horizontal.noUi-marker-large {
    height: 15px
}

.noUi-pips-vertical {
    padding: 0 10px;
    height: 100%;
    top: 0;
    left: 100%
}

.noUi-value-vertical {
    transform: translate(0, -50%);
    padding-left: 25px
}

.noUi-rtl .noUi-value-vertical {
    transform: translate(0, 50%)
}

.noUi-marker-vertical.noUi-marker {
    width: 5px;
    height: 2px;
    margin-top: -1px
}

.noUi-marker-vertical.noUi-marker-sub {
    width: 10px
}

.noUi-marker-vertical.noUi-marker-large {
    width: 15px
}

.noUi-tooltip {
    display: block;
    position: absolute;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    background: #fff;
    color: #000;
    padding: 5px;
    text-align: center;
    white-space: nowrap
}

.noUi-horizontal .noUi-tooltip {
    transform: translate(-50%, 0);
    left: 50%;
    bottom: 120%
}

.noUi-vertical .noUi-tooltip {
    transform: translate(0, -50%);
    top: 50%;
    right: 120%
}

.noUi-horizontal .noUi-origin>.noUi-tooltip {
    transform: translate(50%, 0);
    left: auto;
    bottom: 10px
}

.noUi-vertical .noUi-origin>.noUi-tooltip {
    transform: translate(0, -18px);
    top: auto;
    right: 28px
}

.mobile-nav-trigger path,
.site-nav__compress-menu path {
    transition: all 0.3s cubic-bezier(.18, .77, .58, 1)
}

.mobile-nav-trigger[aria-expanded='true'] path:nth-child(1),
.site-nav__compress-menu[aria-expanded='true'] path:nth-child(1),
.mobile-nav-trigger.is-active path:nth-child(1),
.site-nav__compress-menu.is-active path:nth-child(1) {
    transform: rotate(45deg);
    transform-origin: 20% 30%
}

.mobile-nav-trigger[aria-expanded='true'] path:nth-child(2),
.site-nav__compress-menu[aria-expanded='true'] path:nth-child(2),
.mobile-nav-trigger.is-active path:nth-child(2),
.site-nav__compress-menu.is-active path:nth-child(2) {
    opacity: 0
}

.mobile-nav-trigger[aria-expanded='true'] path:nth-child(3),
.site-nav__compress-menu[aria-expanded='true'] path:nth-child(3),
.mobile-nav-trigger.is-active path:nth-child(3),
.site-nav__compress-menu.is-active path:nth-child(3) {
    transform: rotate(-45deg);
    transform-origin: 15% 66%
}

@media only screen and (min-width:769px) {
    .product-single__sticky {
        position: sticky;
        top: 20px
    }

    .modal--quick-shop .product-single__sticky {
        top: 0
    }

    .product-full-width {
        margin-top: 40px
    }
}

@media only screen and (max-width:768px) {
    .grid--product-images-right {
        display: flex;
        flex-wrap: wrap
    }

    .grid--product-images-right .grid__item:first-child {
        order: 2
    }

    .product-full-width .page-width--narrow {
        max-width: none
    }

    .product-full-width .product-block--tab {
        margin-left: calc(var(--page-width-padding) * -1);
        margin-right: calc(var(--page-width-padding) * -1)
    }

    .product-full-width .product-block--tab .collapsible-trigger-btn {
        padding-left: var(--page-width-padding);
        padding-right: var(--page-width-padding)
    }

    .product-full-width .product-block--tab .collapsible-trigger__icon {
        right: var(--page-width-padding)
    }
}

.page-content--product {
    padding-top: 40px;
    padding-bottom: 0
}

@media only screen and (max-width:768px) {
    .page-content--product {
        padding-top: 15px
    }
}

.modal--quick-shop .page-content--product {
    width: var(--page-width);
    max-width: 100%
}

.product-single__meta {
    padding-top: 65px;
    padding-left: 45px
}

@media only screen and (max-width:768px) {
    .product-single__meta {
        padding-top: 0;
        padding-left: 0;
        margin-top: calc(var(--gutter) / 2);
        margin-bottom: calc(var(--gutter) / 2)
    }
}

.grid--product-images-right .product-single__meta {
    padding-left: 0;
    padding-right: 45px
}

@media only screen and (max-width:768px) {
    .grid--product-images-right .product-single__meta {
        padding-right: 0
    }
}

.product-single__meta .social-sharing {
    margin-top: var(--gutter)
}

.product-single__meta .rte {
    text-align: left
}

html[dir='rtl'] .product-single__meta .rte {
    text-align: right
}

.product-single__meta>*:first-child {
    padding-top: 0
}

.flickity-enabled .product-main-slide:not(.is-selected) button,
.flickity-enabled .product-main-slide:not(.is-selected) video,
.flickity-enabled .product-main-slide:not(.is-selected) iframe,
.flickity-enabled .product-main-slide:not(.is-selected) model-viewer {
    display: none
}

.product-main-slide {
    display: none;
    width: 100%;
    overflow: hidden
}

@media only screen and (max-width:768px) {
    .grid--product-images--partial .product-main-slide {
        width: 75%;
        margin-left: auto;
        margin-right: auto
    }
}

.product-main-slide:first-child {
    display: block
}

@media only screen and (max-width:768px) {
    .product-main-slide {
        border: 1px solid;
        border-color: var(--color-border)
    }
}

[data-media-gallery-layout='stacked'] .product-main-slide {
    display: block;
    margin: 8px auto
}

.flickity-slider .product-main-slide {
    display: block
}

@media only screen and (max-width:768px) {
    .grid--product-images--partial .flickity-slider .product-main-slide {
        width: 75%;
        margin-right: 10px
    }
}

.grid-product__price sup {
    margin-left: 1px;
    font-size: 60%;
    top: -.4em
}

.product__unit-price {
    font-size: calc(var(--type-base-size) - 3px);
    opacity: .65
}

.return-link {
    text-align: center;
    padding: 15px 25px
}

.return-link .icon {
    width: 20px;
    margin-right: 8px
}

@media only screen and (max-width:768px) {
    .product--images {
        order: 1
    }
}

@media only screen and (max-width:768px) {
    .product--description {
        order: 3
    }
}

.index-section {
    margin: var(--index-section-padding) 0
}

.index-section:not(.index-section--sub-product):first-child {
    margin-top: 0;
    padding-top: var(--index-section-padding)
}

.index-section+.index-section,
.index-section+.index-section--hidden {
    margin-top: 0
}

.page-width .index-section .page-width {
    padding-left: 0;
    padding-right: 0
}

.index-section--flush {
    margin: 0
}

.section--divider {
    border-top: 1px solid;
    border-top-color: var(--color-border);
    padding-top: var(--index-section-padding)
}

.template-challange .index-section--footer {
    display: none
}

.theme-block {
    margin-bottom: 30px
}

.theme-block:last-child {
    margin-bottom: 0
}

.article-tag {
    display: inline-block;
    background-color: var(--color-body);
    color: var(--color-text-body);
    border: 1px solid;
    border-color: var(--color-text-body);
    padding: 3px 9px;
    margin: 5px;
    font-size: 12px;
    font-weight: var(--type-header-weight);
    letter-spacing: .15em;
    text-transform: uppercase
}

.tag--inline .article-tag {
    margin: 0
}

.article-tag:hover,
.article-tag:active {
    background-color: var(--color-body);
    color: var(--color-text-body)
}

@media only screen and (max-width:768px) {
    .article-tag {
        padding: 4px 6px;
        font-size: 10px;
        margin: 3px
    }
}

.article__sub-meta {
    opacity: .65;
    font-size: calc(var(--type-base-size) - 2px)
}

.article__sub-meta>span {
    position: relative;
    display: inline-block;
    margin-bottom: 2px;
    margin-right: 10px
}

[dir='rtl'] .article__sub-meta>span {
    margin-right: 0;
    margin-left: 10px
}

.article__sub-meta>span:not(:last-child):after {
    content: '\b7';
    display: inline-block;
    margin-left: 10px
}

[dir='rtl'] .article__sub-meta>span:not(:last-child):after {
    margin-left: 0;
    margin-right: 10px
}

.section-header .article__sub-meta {
    margin-top: 20px
}

.article__sub-meta-date {
    text-transform: uppercase
}

.article__title {
    font-weight: var(--type-header-weight)
}

[data-color-swatch],
.color-swatch {
    position: relative;
    display: block;
    text-indent: -9999px;
    overflow: hidden;
    margin: 0 4px 4px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    width: 2.5em;
    border: 3px solid;
    border-color: var(--color-body);
    border-radius: var(--swatch-border-radius);
    box-shadow: 0 0 0 1px var(--color-border);
    transition: box-shadow 0.1s ease;
    width: var(--swatch-size);
    height: var(--swatch-size)
}

.is-active[data-color-swatch],
.color-swatch.is-active {
    box-shadow: 0 0 0 1px var(--color-text-body)
}

.modal {
    display: none;
    bottom: 0;
    left: 0;
    opacity: 1;
    overflow: hidden;
    position: fixed;
    right: 0;
    top: 0;
    z-index: var(--z-index-modal);
    color: #fff;
    align-items: center;
    justify-content: center
}

.modal.modal--quick-shop {
    align-items: flex-start
}

.modal a:not(.btn),
.modal a:not(.btn):hover {
    color: inherit
}

.modal-open .modal .modal__inner {
    animation: modal-open 0.3s forwards
}

.modal-open .modal:before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-modal-bg);
    animation: overlay-on 0.3s forwards;
    cursor: pointer
}

.modal-closing .modal .modal__inner {
    animation: modal-closing 0.15s forwards
}

.modal-closing .modal:before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-modal-bg);
    animation: overlay-off 0.15s forwards
}

.modal-open--solid .modal:before {
    background-color: var(--color-modal-bg)
}

.modal--is-closing {
    display: flex !important;
    overflow: hidden
}

.modal--is-active {
    display: flex !important;
    overflow: hidden
}

@media only screen and (min-width:769px) {
    .modal-open {
        overflow: hidden;
        scrollbar-gutter: stable
    }
}

.modal__inner {
    transform-style: preserve-3d;
    flex: 0 1 auto;
    margin: calc(var(--gutter) / 2);
    max-width: calc(100% - 30px);
    display: flex;
    align-items: center;
    box-shadow: 0 12px 25px rgb(0 0 0 / 15%);
    border-radius: var(--roundness)
}

@media only screen and (min-width:769px) {
    .modal__inner {
        margin: 40px;
        max-width: calc(100% - 80px)
    }
}

.modal--square .modal__inner {
    background-color: var(--color-body);
    color: var(--color-text-body);
    border-radius: var(--roundness)
}

.modal__inner .scheme-image {
    border-radius: calc(var(--roundness) + 2px)
}

.modal__inner .image-wrap img {
    max-height: none
}

.modal__centered {
    position: relative;
    flex: 0 1 auto;
    min-width: 1px;
    max-width: 100%
}

.modal--square .modal__centered-content {
    max-height: 80vh;
    padding: 30px;
    min-width: 200px;
    min-height: 200px;
    border-radius: var(--roundness);
    overflow: auto;
    -webkit-overflow-scrolling: touch
}

@media only screen and (min-width:769px) {
    .modal--square .modal__centered-content {
        padding: calc(var(--gutter) * 1.5);
        max-height: 90vh;
        max-width: 1200px
    }
}

.modal--square .modal__centered-content--padded {
    padding: 60px
}

.modal__close {
    border: 0;
    padding: 6px;
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(25%, -25%);
    z-index: 2;
    transition: transform 0.15s ease-out
}

.modal__close:hover {
    transform: translate(25%, -25%) scale(1.08)
}

.modal__close .icon {
    width: 28px;
    height: 28px
}

.modal--is-closing .modal__close {
    display: none
}

.modal .page-content,
.modal .page-width {
    padding: 0
}

@media only screen and (max-width:768px) {
    .modal--mobile-friendly {
        top: auto;
        bottom: 20px;
        overflow: auto
    }

    .modal--mobile-friendly.modal--square .modal__centered-content {
        padding: 30px
    }

    .modal--mobile-friendly.modal--is-active {
        overflow: visible
    }

    .modal-open .modal--mobile-friendly:before {
        display: none
    }

    .modal--mobile-friendly .modal__inner {
        margin: 0
    }

    .modal--mobile-friendly .h1 {
        padding-right: 25px
    }

    .modal--mobile-friendly input {
        font-size: 16px !important
    }

    .modal--mobile-friendly .text-close {
        display: none
    }
}

.modal__footer-text {
    padding: calc(var(--gutter) / 2) var(--gutter);
    text-align: center
}

.modal__footer-text a {
    color: currentColor;
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

:root {
    --z-index-hero-image: 1;
    --z-index-hero-video: 2;
    --z-index-hero-image-overlay: 3;
    --z-index-hero-text: 4;
    --slideshow-image-animation-amount: 200px;
    --slideshow-image-animation-speed: 0.5s
}

.hero--padded {
    margin-top: var(--page-width-padding);
    margin-bottom: var(--page-width-padding)
}

.index-section--hero:first-child .hero--padded {
    margin-top: 0;
    padding-top: var(--page-width-padding)
}

.hero {
    display: block;
    position: relative;
    overflow: hidden;
    background-color: var(--color-large-image-bg);
    color: #fff
}

.hero.loaded {
    background-color: #fff0;
    transition: background-color 0.2s ease 0.3s
}

.hero--padded .hero {
    border-radius: var(--roundness)
}

.hero--padded .hero .hero__media {
    overflow: hidden;
    border-radius: var(--roundness)
}

.hero__image-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%
}

.hero__image {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: var(--z-index-hero-image);
    object-fit: cover
}

.vimeo-mobile-trigger {
    display: block;
    position: absolute;
    width: 100%;
    z-index: 2;
    margin-top: 90px
}

.hero__text-content .vimeo-mobile-trigger {
    bottom: 120%
}

.vimeo-mobile-trigger .icon {
    width: 40px;
    height: 40px;
    background-color: #fff;
    border-radius: 50%;
    padding: 10px
}

.hero__slide-link {
    display: block;
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: calc(var(--z-index-hero-text) + 1)
}

.hero__slide-link:hover~.hero__text-wrap .btn:not(.btn--secondary):not(.btn--inverse) {
    background: var(--color-button-primary-light);
    transition-delay: 0.25s
}

.hero__text-wrap {
    position: relative;
    height: 100%
}

.video-interactable .hero__text-wrap {
    pointer-events: none
}

.hero__text-wrap .page-width {
    display: table;
    width: 100%;
    height: 100%
}

.hero__text-content {
    position: relative;
    padding: calc(var(--gutter) / 2) 0;
    z-index: var(--z-index-hero-text)
}

@media only screen and (min-width:769px) {
    .hero__text-content {
        padding: calc(var(--gutter) * 1.5) 0
    }
}

[data-dots='true'] .hero__text-content {
    padding-bottom: 40px
}

.hero__text-shadow {
    --z-index-overlay: -1;
    position: relative;
    display: inline-block;
    text-shadow: 0 3px 7px rgb(0 0 0 / .15)
}

.hero__text-shadow:after {
    background: radial-gradient(rgb(0 0 0 / .3) 0%, transparent 60%);
    margin: -100px -200px -100px -200px
}

.hero__text-shadow .btn {
    text-shadow: none
}

.horizontal-left .hero__text-shadow {
    max-width: 500px
}

.horizontal-right .hero__text-shadow {
    max-width: 500px;
    text-align: left
}

@media only screen and (max-width:768px) {
    .horizontal-left .hero__text-shadow {
        max-width: 85%
    }

    .horizontal-right .hero__text-shadow {
        max-width: 85%
    }
}

.hero__top-subtitle,
.hero__title,
.hero__subtitle {
    margin-bottom: 10px
}

@media only screen and (min-width:769px) {

    .hero__top-subtitle,
    .hero__title,
    .hero__subtitle {
        margin-bottom: 15px
    }
}

.hero__top-subtitle {
    letter-spacing: .07em;
    font-size: 1.1em
}

.hero__title {
    display: block
}

.hero__title p {
    margin-bottom: 0
}

.hero__subtext {
    margin-top: 20px
}

.hero__subtitle {
    display: block;
    vertical-align: middle
}

@media only screen and (min-width:769px) {
    .hero__subtitle {
        font-size: 1.3em
    }
}

.hero__link {
    position: relative;
    display: block
}

.video-interactable .hero__link {
    pointer-events: auto
}

.hero__link .btn {
    margin: 4px 15px 15px 0
}

@media only screen and (max-width:768px) {
    .hero__link .btn {
        margin: 4px 10px 6px 0
    }

    .small--text-center .hero__link .btn {
        margin: 4px 10px 6px
    }
}

.hero__link .btn .icon-play {
    position: relative;
    top: -2px;
    margin-right: 5px
}

.hero__link .btn--inverse {
    color: currentColor;
    border-color: currentColor
}

.hero__text-content {
    display: table-cell
}

.hero__text-content .hero__link {
    margin-top: calc(var(--gutter) / 4)
}

@media only screen and (min-width:769px) {
    .hero__text-content .hero__link {
        margin-top: calc(var(--gutter) / 2)
    }
}

.hero__text-content.horizontal-left {
    text-align: left
}

.hero__text-content.horizontal-center {
    text-align: center
}

.hero__text-content.horizontal-right {
    text-align: right
}

.hero__text-content.vertical-center {
    vertical-align: middle
}

.hero__text-content.vertical-bottom {
    vertical-align: bottom
}

.overlaid-header .index-section--hero:first-child .hero__text-content.vertical-center {
    padding-top: 50px
}

@media only screen and (min-width:769px) {
    .overlaid-header .index-section--hero:first-child .hero__text-content.vertical-center {
        padding-top: 90px
    }
}

.hero[data-natural] {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.hero .slideshow__slide .hero__animation-contents {
    opacity: 0;
    transform: translateY(10px);
    transition: none
}

.no-js .hero .slideshow__slide .hero__animation-contents {
    opacity: 1
}

@media only screen and (max-width:768px) {
    .hero .slideshow__slide .hero__animation-contents {
        transform: translateY(7px)
    }
}

.hero .slideshow__slide .hero__text-shadow:after {
    opacity: 0
}

.hero .slideshow__slide:not(.animate-out) .hero__image {
    opacity: 0;
    transition: none
}

.hero .slideshow__slide .hero__link .btn {
    opacity: 0;
    transition: none
}

.no-js .hero .slideshow__slide .hero__link .btn {
    opacity: 1
}

.hero .slideshow__slide .hero__image-wrapper {
    transform: scale(1.15);
    opacity: 0;
    transition: none
}

.hero .slideshow__slide .hero__sidebyside {
    opacity: 0;
    transition: none
}

.no-js .hero .slideshow__slide .hero__sidebyside {
    opacity: 1
}

.hero .slideshow__slide .hero__sidebyside-image .hero__image {
    opacity: 0;
    transform: scale(1.15);
    transition: none
}

.no-js .hero .slideshow__slide .hero__sidebyside-image .hero__image {
    opacity: 1
}

.hero.loaded .slideshow__slide.is-selected .hero__animation-contents {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.4s ease, transform 0.6s cubic-bezier(.26, .54, .32, 1)
}

.hero.loaded .slideshow__slide.is-selected .hero__link .btn {
    opacity: 1;
    transition: opacity 1s ease
}

.hero.loaded .slideshow__slide.is-selected .hero__text-shadow:after {
    opacity: 1;
    transition: all 0.4s ease 0.2s
}

.hero.loaded .slideshow__slide.is-selected .hero__title .hero__animation-contents {
    transition-delay: 0.2s
}

.hero.loaded .slideshow__slide.is-selected .hero__subtitle .hero__animation-contents {
    transition-delay: 0.4s
}

.hero.loaded .slideshow__slide.is-selected .hero__link .btn {
    transition-delay: 0.6s
}

.hero.loaded .slideshow__slide.is-selected .hero__link .btn:nth-child(2) {
    transition-delay: 0.8s
}

.hero.loaded .slideshow__slide.is-selected .hero__media,
.hero.loaded .slideshow__slide.is-selected .hero__image,
.hero.loaded .slideshow__slide.is-selected .hero__image--svg {
    opacity: 1;
    transition: none
}

.hero.loaded .slideshow__slide.is-selected .hero__image-wrapper {
    opacity: 1;
    transform: scale(1);
    transition: transform 1s cubic-bezier(.18, .63, .25, 1), opacity 0.7s ease
}

.hero.loaded .slideshow__slide.is-selected .hero__sidebyside {
    opacity: 1;
    transition: opacity calc(var(--slideshow-image-animation-speed) + 0.2s) ease
}

.hero.loaded .slideshow__slide.is-selected .hero__sidebyside-image .hero__image {
    opacity: 1;
    transform: scale(1);
    animation: none;
    transition: transform calc(var(--slideshow-image-animation-speed) + 0.2s) cubic-bezier(.13, .55, .25, 1)
}

.hero .slideshow__slide.animate-out .hero__animation-contents {
    opacity: 0;
    transition: none
}

.hero .slideshow__slide.animate-out .hero__image-wrapper {
    opacity: 0;
    transform: scale(1);
    transition: transform var(--slideshow-image-animation-speed) ease-in 0.05s, opacity var(--slideshow-image-animation-speed) ease-in 0.05s
}

:root {
    --announcement-animation-speed: 0.5s
}

.announcement-bar {
    position: relative;
    font-size: 13px;
    font-weight: var(--type-header-weight);
    text-align: center;
    padding: 7px 0
}

@media only screen and (min-width:769px) {
    .announcement-bar {
        padding: 6px 0;
        font-size: 14px;
        text-align: left
    }

    [dir='rtl'] .announcement-bar {
        text-align: right
    }
}

.announcement-slider__slide {
    position: relative;
    overflow: hidden;
    width: 100%
}

.announcement-slider__content {
    opacity: 0;
    transform: translateY(100%);
    transition: all var(--announcement-animation-speed) ease
}

.announcement-slider__content p {
    margin: 0
}

.announcement-slider__content a {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.animate-out .announcement-slider__content {
    opacity: 0;
    transform: translateY(-100%)
}

.is-selected .announcement-slider__content {
    opacity: 1;
    transform: translateY(0)
}

.grid-article {
    flex: 0 0 100%;
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    margin-bottom: 40px
}

@media only screen and (min-width:769px) {

    .grid-article[data-style='medium'],
    .grid-article[data-style='large'] {
        display: block
    }
}

.grid-article:last-child {
    margin-bottom: 0
}

.grid-article__image {
    position: relative;
    flex: 0 0 100%
}

.grid-article__image .image-wrap {
    border-radius: var(--roundness)
}

.grid-article__tags {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    overflow: visible;
    text-align: center
}

.grid-article__tags .article-tag {
    transform: translateY(-50%)
}

.grid-article__meta {
    flex: 0 0 100%
}

.grid-article[data-style='large'] .article__title {
    font-size: calc(var(--type-base-size) + 10px)
}

@media only screen and (max-width:768px) {
    .grid-article[data-style='large'] .article__title {
        font-size: calc(var(--type-base-size) + 7px)
    }
}

.grid-article[data-style='large'] .article__sub-meta>span {
    margin: 0 5px 2px
}

.grid-article[data-style='medium'] {
    flex: 0 0 50%
}

.grid-article[data-style='medium'] .article__title {
    font-size: calc(var(--type-base-size) + 2px)
}

@media only screen and (min-width:769px) {
    .grid-article[data-style='medium'] .article__sub-meta>span {
        margin: 0 5px 2px
    }
}

.grid-article[data-style='compact'] {
    flex: 0 0 100%;
    flex-wrap: nowrap;
    text-align: left;
    margin-bottom: 20px;
    padding: 0
}

.grid-article[data-style='compact'] .grid-article__image {
    flex: 0 0 40%;
    align-self: flex-start
}

.grid-article[data-style='compact'] .grid-article__meta {
    flex: 0 0 60%;
    padding-left: 15px
}

.grid-article[data-style='compact'] .article__title {
    font-size: calc(var(--type-base-size) + 1px)
}

.grid-article[data-style='compact'] .article-tag {
    font-size: 10px
}

.grid-article[data-style='compact'] .article-tag:not(:first-child) {
    display: none
}

@media only screen and (max-width:768px) {
    .grid-article[data-style='medium'] {
        flex: 0 0 100%;
        flex-wrap: nowrap;
        text-align: left;
        margin-bottom: 20px
    }

    .grid-article[data-style='medium'] .grid-article__image {
        flex: 0 0 40%;
        align-self: center
    }

    .grid-article[data-style='medium'] .grid-article__meta {
        flex: 0 0 60%;
        padding-left: 15px
    }

    .grid-article[data-style='medium'] .article__title {
        font-size: calc(var(--type-base-size) + 1px)
    }

    .grid-article[data-style='medium'] .article-tag {
        font-size: 10px
    }

    .grid-article[data-style='medium'] .article-tag:not(:first-child) {
        display: none
    }
}

.add-to-cart[disabled]+.shopify-payment-button {
    display: none
}

.product__policies {
    font-size: .85em
}

.shopify-payment-terms {
    margin: 15px 0
}

.shopify-payment-terms:empty {
    display: none
}

.quick-shop-modal .shopify-payment-terms {
    display: none
}

[data-on-sale] .block-price__regular {
    display: none
}

.block-price__save {
    display: none
}

[data-on-sale] .block-price__save {
    display: block
}

.block-price__sale {
    display: none
}

[data-on-sale] .block-price__sale {
    display: flex;
    gap: 10px
}

.block-price__unit-price {
    display: none
}

[data-unit-price] .block-price__unit-price {
    display: block
}

.block-price__container {
    display: flex;
    align-items: baseline;
    flex-wrap: wrap
}

.product__price {
    color: var(--color-price);
    margin-right: 5px;
    font-size: calc(var(--type-base-size) + 3px);
    font-weight: 400
}

@media only screen and (min-width:769px) {
    .product__price {
        font-size: calc(var(--type-base-size) + 6px)
    }
}

.product__price sup {
    margin-left: 1px;
    font-size: 60%;
    top: -.4em
}

.product__price-savings {
    color: var(--color-text-savings);
    white-space: nowrap
}

.product__quantity label {
    display: block;
    margin-bottom: 10px
}

.sales-points {
    list-style: none;
    padding: 0;
    margin: 0
}

.quick-add-modal .sales-points {
    display: none
}

.sales-point {
    display: block;
    margin-bottom: 10px
}

.sales-point:last-child {
    margin-bottom: 0
}

.sales-point .icon {
    position: relative;
    width: 25px;
    height: 25px;
    margin-right: 10px
}

[dir='rtl'] .sales-point .icon {
    margin-right: 0;
    margin-left: 10px
}

.product-block--sales-point+.product-block--sales-point {
    margin-top: -20px
}

.product-block.product-block--tab {
    margin-bottom: 30px
}

.product-block--tab+.product-block--tab {
    margin-top: -30px
}

.product-single__title {
    margin-bottom: 20px;
    word-wrap: break-word
}

.product-single__vendor-sku {
    opacity: .65
}

.product-single__vendor-sku>span {
    display: inline-block;
    margin-right: 20px
}

.product-single__vendor {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.product-single__sku {
    font-size: calc(var(--type-base-size) - 3px)
}

.block-variant-picker {
    display: grid;
    gap: 25px
}

.block-variant-picker[data-picker-type='dropdown'] {
    display: block;
    gap: normal
}

.breadcrumb {
    font-size: calc(var(--type-base-size) * 0.85);
    margin: 0 0 18px
}

@media only screen and (max-width:768px) {
    .breadcrumb {
        margin-bottom: calc(var(--gutter) / 2)
    }
}

.breadcrumb__divider {
    color: currentColor;
    opacity: .2;
    padding: 0 5px
}

.cart__item {
    display: grid;
    grid-template-areas: 'cartImage cartTitle .' 'cartImage cartQuantity cartPrice' '. cartRemove cartPrice';
    grid-template-columns: 150px auto auto;
    grid-column-gap: 20px;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid;
    border-bottom-color: var(--color-border)
}

.site-header__cart .cart__item {
    grid-template-columns: 100px auto auto;
    grid-column-gap: 10px
}

@media only screen and (max-width:959px) {
    .cart__item {
        grid-template-areas: 'cartImage cartTitle cartTitle' 'cartImage cartQuantity cartPrice' 'cartImage cartRemove cartPrice'
    }
}

.cart__item:last-of-type {
    border-bottom: 0
}

.cart__image {
    grid-area: cartImage;
    align-self: flex-start
}

.cart__image a {
    position: relative;
    display: block;
    width: 100%;
    height: 100px
}

.cart__image img {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: block;
    object-fit: contain
}

.cart__item-title {
    grid-area: cartTitle
}

.cart__item-quantity {
    grid-area: cartQuantity
}

.cart__item-remove {
    grid-area: cartRemove
}

.cart__item-remove a {
    display: inline-block;
    margin-top: 10px
}

.cart__drawer-form .cart__item-remove {
    display: none
}

.cart__item-price {
    grid-area: cartPrice
}

.cart__item-name {
    display: block;
    font-size: calc(var(--type-base-size) + 1px);
    margin-bottom: 8px
}

.cart__item--variants {
    font-size: calc(var(--type-base-size) - 2px);
    margin-bottom: 10px
}

.cart__item--variants span {
    font-weight: var(--type-header-weight)
}

.cart__price {
    display: block
}

.cart__price:not(.cart__price--strikethrough) {
    font-weight: var(--type-header-weight);
    font-size: calc(var(--type-base-size) + 2px)
}

.cart__price--strikethrough {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.cart__item--properties {
    font-size: calc(var(--type-base-size) - 2px);
    margin-bottom: 10px;
    line-height: 1.3
}

.cart__item--properties span {
    font-weight: 700
}

.note-icon svg {
    position: relative;
    top: -1px;
    width: 16px;
    height: 16px;
    margin-left: 5px
}

.add-note {
    margin-bottom: 20px
}

.add-note.is-active .note-icon--open {
    display: none
}

.note-icon--close {
    display: none
}

.add-note.is-active .note-icon--close {
    display: inline
}

.cart__recommended-title {
    margin-bottom: 20px
}

.collapsible-trigger {
    color: inherit;
    position: relative
}

.collapsible-trigger__layout {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.collapsible-trigger__layout>span {
    display: block;
    padding-right: 10px
}

.collapsible-trigger__layout--inline {
    position: relative;
    justify-content: flex-start
}

.collapsible-trigger__layout--inline>span {
    padding-right: 15px
}

.collapsible-trigger__layout--inline .collapsible-trigger__icon {
    position: static;
    transform: none
}

.collapsible-trigger__icon {
    display: block;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: var(--collapsible-icon-width);
    height: var(--collapsible-icon-width)
}

.collapsible-trigger__icon .icon {
    display: block;
    width: var(--collapsible-icon-width);
    height: var(--collapsible-icon-width);
    transition: all 0.1s ease-in
}

.collapsible-trigger.is-open .collapsible-trigger__icon>.icon-chevron-down {
    transform: rotate(180deg)
}

.collapsible-trigger--inline {
    font-weight: var(--type-header-weight);
    padding: 11px 0 11px 20px
}

.collapsible-trigger--inline .collapsible-trigger__icon {
    right: auto;
    left: 0
}

.collapsible-content {
    transition: opacity 0.2s ease, height 0.15s ease, transform 0.3s cubic-bezier(.25, .46, .45, .94);
    transform: translateY(-10px)
}

.collapsible-content.is-open {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, height 0.25s ease, transform 0.3s cubic-bezier(.25, .46, .45, .94)
}

.collapsible-content--all {
    visibility: hidden;
    overflow: hidden;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    opacity: 0;
    height: 0
}

@media only screen and (min-width:769px) {
    .collapsible-content--all.is-open {
        overflow: initial;
        visibility: visible;
        opacity: 1;
        height: auto
    }
}

.collapsible-content--all.is-open {
    border-bottom: 1px solid;
    border-color: var(--color-border)
}

.filter-wrapper .collapsible-content--all.is-open {
    border: 0
}

@media only screen and (max-width:768px) {
    .collapsible-content--small {
        visibility: hidden;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        opacity: 0;
        height: 0
    }

    .collapsible-content--small .collapsible-content__inner {
        transform: translateY(40px)
    }
}

@media only screen and (min-width:769px) {
    .collapsible-content__inner {
        padding: 0 0 35px
    }
}

.collapsible-trigger[aria-expanded='true'] .collapsible-label__closed {
    display: none
}

.collapsible-label__open {
    display: none
}

.collapsible-trigger[aria-expanded='true'] .collapsible-label__open {
    display: inline-block
}

.collection-item {
    position: relative;
    display: block;
    flex: 1 1 100%;
    text-align: center;
    margin-bottom: 5px
}

@media only screen and (min-width:769px) {
    .collection-item {
        margin-bottom: 15px
    }
}

.collection-image-wrap {
    position: relative;
    transition: all 0.2s ease
}

.collection-image-wrap.collection-image-color--undefined {
    --z-index-overlay: 1
}

.collection-image-wrap.collection-image-color--undefined:after {
    background-color: rgb(0 0 0 / .027);
    pointer-events: none;
    transition: all 0.2s ease;
    border-radius: var(--roundness)
}

.collection-image-wrap.collection-image-color--white:before {
    background-color: #fff;
    transition: all 0.2s ease;
    border-radius: var(--roundness)
}

.collection-image-wrap.collection-image-color--grey {
    --z-index-overlay: 1
}

.collection-image-wrap.collection-image-color--grey:after {
    background-color: rgb(0 0 0 / .027);
    pointer-events: none;
    transition: all 0.2s ease;
    border-radius: var(--roundness)
}

.collection-image-wrap.collection-image--circle:hover {
    border-radius: 50%
}

.collection-image-wrap:hover {
    transform: translateY(-3px);
    box-shadow: 0 3px 10px rgb(0 0 0 / .1);
    border-radius: var(--roundness)
}

.collection-image-wrap:hover:after {
    background-color: #fff0
}

.collection-item:active .collection-image-wrap {
    transform: scale(.97);
    transition: transform 0.05s ease-out
}

.collection-image {
    position: static
}

.collection-image img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: contain;
    padding: var(--collection-tile-margin)
}

.collection-image img,
.collection-image svg {
    border-radius: var(--roundness)
}

.collection-image--is-collection img,
.collection-image-fill-space--true img {
    object-fit: cover
}

.collection-image--placeholder {
    opacity: 1
}

.collection-image--placeholder svg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.collection-image--circle {
    border-radius: 50%;
    overflow: hidden
}

.collection-image--circle:after {
    border-radius: 50%
}

.collection-image--circle,
.collection-image--square {
    padding-bottom: 100%
}

.collection-image--landscape {
    padding-top: 75%
}

.collection-image--portrait {
    padding-top: 150%
}

.collection-item__title {
    display: block;
    margin-top: 12px;
    font-weight: var(--type-header-weight);
    padding: 0 7%
}

.overlay--before::before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: var(--z-index-overlay, auto)
}

.scheme-image,
.scheme-darken,
.scheme-squiggle,
.scheme-swirl,
.scheme-dots,
.scheme-notebook,
.scheme-wave,
.scheme-minimal-wave,
.scheme-plants,
.scheme-cold-blur,
.scheme-warm-blur,
.scheme-custom-texture-1,
.scheme-custom-texture-2,
.scheme-custom-texture-3 {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1
}

[data-animate].scheme-image {
    object-fit: cover;
    pointer-events: none;
    mix-blend-mode: multiply;
    opacity: 1
}

.scheme-image[data-animate][data-texture='space.jpg'] {
    mix-blend-mode: screen
}

[data-animate].scheme-texture--linen,
[data-animate].scheme-texture--sand,
[data-animate].scheme-texture--stone,
[data-animate].scheme-texture--wildflower {
    opacity: .24
}

.scheme-darken {
    --z-index-overlay: 1;
    z-index: 1;
    pointer-events: none
}

.scheme-darken:after {
    background-color: rgb(0 0 0 / .027);
    pointer-events: none
}

.scheme-swirl {
    background-image: var(--url-swirl-svg);
    opacity: .12
}

.scheme-squiggle {
    background: url("data:image/svg+xml,%3Csvg width='150' height='75' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.17'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
}

.scheme-dots {
    background-image: radial-gradient(rgb(0 0 0 / .15) .5px, transparent 2px);
    background-size: 16px 16px
}

.scheme-notebook {
    background-image: url(notebook.svg);
    opacity: .5
}

.scheme-wave {
    background-image: url(wave.svg);
    background-size: cover;
    opacity: .5
}

.scheme-minimal-wave {
    background-image: url(minimal-wave.svg);
    opacity: .5;
    background-size: cover
}

.scheme-plants {
    background-image: url(plants.svg);
    background-size: cover
}

.scheme-cold-blur {
    background-image: url(cold-blur.svg);
    background-size: cover
}

.scheme-warm-blur {
    background-image: url(warm-blur.svg);
    background-size: cover
}

.scheme-custom-texture-1 {
    background-image: url(custom-texture-1.svg);
    background-size: cover
}

.scheme-custom-texture-2 {
    background-image: url(custom-texture-2.svg);
    background-size: cover
}

.scheme-custom-texture-3 {
    background-image: url(custom-texture-3.svg);
    background-size: cover
}

.content-over-media {
    --container-max-width: calc(var(--page-width) - var(--page-width-padding) * 2);
    --container-gutter: 40px;
    --content-over-media-gap: var(--container-gutter);
    --content-over-media-column-gap: var(--content-over-media-gap);
    --content-over-media-row-gap: var(--content-over-media-gap);
    display: grid;
    grid-template-columns: minmax(0, 1fr) minmax(0, min(var(--container-max-width), 100% - var(--content-over-media-column-gap) * 2)) minmax(0, 1fr);
    grid-template-rows: 0 minmax(0, 1fr) 0;
    gap: var(--content-over-media-row-gap) var(--content-over-media-column-gap);
    place-items: center;
    position: relative;
    overflow: hidden
}

.content-over-media[overlay='true']:before {
    content: '';
    background-color: rgb(0 0 0 / .3);
    pointer-events: none;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1
}

.content-over-media>:not(.content-over-media__content) {
    position: relative;
    grid-area: 1 / 1 / span 3 / span 3;
    place-self: stretch;
    width: 100%;
    height: var(--content-over-media-height);
    min-height: 100%;
    max-height: 100%;
    object-fit: cover;
    object-position: center;
    overflow-wrap: anywhere;
    transform-origin: top;
    -webkit-user-select: none;
    user-select: none
}

.content-over-media__content {
    color: #fff;
    position: relative;
    grid-row-start: 2;
    grid-column-start: 2;
    grid-row-end: auto;
    grid-column-end: span 1;
    max-width: var(--content-over-media-content-max-width, 650px);
    z-index: 1
}

.content-over-media[data-height='small'] {
    --content-over-media-height: 400px
}

.content-over-media[data-height='medium'] {
    --content-over-media-height: 600px
}

.content-over-media[data-height='large'] {
    --content-over-media-height: 800px
}

.content-over-media[data-height='full'] {
    --content-over-media-height: 100vh
}

picture img {
    width: 100%;
    max-height: 100%;
    min-height: inherit
}

.content-over-media iframe {
    position: absolute;
    top: 50%;
    width: 100%;
    transform: translateY(-50%);
    aspect-ratio: 16 / 9
}

.content-over-media__content[data-mobile-content-position='start'] {
    place-self: start;
    text-align: left
}

.content-over-media__content[data-mobile-content-position='top-center'] {
    place-self: start center;
    text-align: center
}

.content-over-media__content[data-mobile-content-position='top-end'] {
    place-self: start end;
    text-align: right
}

.content-over-media__content[data-mobile-content-position='center-start'] {
    place-self: center start;
    text-align: left
}

.content-over-media__content[data-mobile-content-position='center'] {
    place-self: center center;
    text-align: center
}

.content-over-media__content[data-mobile-content-position='center-end'] {
    place-self: center end;
    text-align: right
}

.content-over-media__content[data-mobile-content-position='end-start'] {
    place-self: end start;
    text-align: left
}

.content-over-media__content[data-mobile-content-position='end-center'] {
    place-self: end center;
    text-align: center
}

.content-over-media__content[data-mobile-content-position='end'] {
    place-self: end end;
    text-align: right
}

@media only screen and (min-width:769px) {
    .content-over-media__content[data-desktop-content-position='start'] {
        place-self: start;
        text-align: left
    }

    .content-over-media__content[data-desktop-content-position='top-center'] {
        place-self: start center;
        text-align: center
    }

    .content-over-media__content[data-desktop-content-position='top-end'] {
        place-self: start end;
        text-align: right
    }

    .content-over-media__content[data-desktop-content-position='center-start'] {
        place-self: center start;
        text-align: left
    }

    .content-over-media__content[data-desktop-content-position='center'] {
        place-self: center center;
        text-align: center
    }

    .content-over-media__content[data-desktop-content-position='center-end'] {
        place-self: center end;
        text-align: right
    }

    .content-over-media__content[data-desktop-content-position='end-start'] {
        place-self: end start;
        text-align: left
    }

    .content-over-media__content[data-desktop-content-position='end-center'] {
        place-self: end center;
        text-align: center
    }

    .content-over-media__content[data-desktop-content-position='end'] {
        place-self: end end;
        text-align: right
    }
}

.recipient-form {
    display: block;
    position: relative;
    margin: 2em auto
}

.recipient-form .field {
    position: relative
}

.recipient-form .field__label {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    color: inherit;
    padding: 8px 10px;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
    margin: 0
}

@media only screen and (max-width:959px) {
    .recipient-form .field__label {
        padding: 10px
    }
}

.recipient-form .field__label:not(.variant__button-label):not(.text-label),
.recipient-form label:not(.variant__button-label):not(.text-label) {
    font-weight: 400
}

.recipient-form .field__input {
    margin-bottom: 1.5em
}

@media only screen and (max-width:959px) {
    .recipient-form .field__input {
        margin-bottom: .6em
    }
}

.recipient-form .field__input.text-area {
    margin-bottom: 0;
    height: 150px
}

.recipient-form .field__input::-webkit-input-placeholder {
    color: #fff0
}

.recipient-form .field__input::-moz-placeholder {
    color: #fff0
}

.recipient-form .field__input:focus~label,
.recipient-form .field__input:not(:placeholder-shown)~label {
    opacity: .65;
    transform: scale(.85) translateY(-.4em) translateX(.15em);
    font-size: .8em
}

.recipient-form .field__input:focus,
.recipient-form .field__input:not(:placeholder-shown) {
    padding: 16px 10px 0
}

.recipient-form .field__input--error {
    border-color: var(--error-red);
    background-color: var(--error-red-bg);
    color: var(--error-red)
}

.recipient-form .field__input--error~label {
    color: var(--error-red)
}

.recipient-form .recipient-form__checkbox-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 2em
}

@media only screen and (max-width:959px) {
    .recipient-form .recipient-form__checkbox-wrapper {
        margin-bottom: 1.5em
    }
}

.recipient-form input[type='checkbox'] {
    height: 16px;
    width: 16px;
    background-color: inherit;
    border: 1px solid;
    border-color: var(--color-border);
    border-radius: var(--input-radius)
}

.recipient-form .checkbox-label {
    display: block;
    cursor: pointer;
    margin-bottom: 0
}

.recipient-form .recipient-form-field-label--space-between>span {
    font-size: .8em
}

.recipient-form .recipient-fields {
    display: none
}

.site-header__cart-empty {
    display: none;
    padding: 20px
}

.is-empty .site-header__cart-empty {
    display: block
}

.cart-drawer.is-empty form {
    display: none
}

.cart__drawer-form {
    max-height: 75vh;
    max-height: calc(100dvh - var(--header-nav-height, 0px));
    display: flex;
    flex-direction: column
}

.cart__scrollable {
    overflow-y: auto;
    padding: 20px 20px 0
}

.cart__scrollable .scrollable-grid {
    padding-top: 20px;
    padding-left: 20px;
    margin-left: -20px;
    padding-bottom: 2px
}

.cart__footer {
    padding: 20px;
    border-top: 1px solid;
    border-top-color: var(--color-border)
}

.cart__note {
    margin-bottom: 20px
}

.site-nav__dropdown.megamenu,
.megamenu {
    padding: 40px 0 0;
    line-height: 1.8
}

.text-center .site-navigation {
    margin: 0 auto
}

.site-navigation--below {
    margin-left: calc(var(--site-nav-item-padding) * -1)
}

.site-navigation--below:before {
    position: absolute;
    left: 0;
    right: 0;
    content: '';
    display: block;
    border-top: 1px solid;
    border-top-color: var(--color-nav-text);
    width: 100%;
    opacity: .1;
    transition: opacity 0.5s ease 0.3s;
    z-index: 1
}

.is-light .site-navigation--below:before {
    border-color: var(--color-sticky-nav-links)
}

.header-wrapper--compressed .site-navigation--below:before {
    opacity: 0
}

.site-header__element.is-active .site-navigation--below:before {
    opacity: .15
}

.site-nav__item {
    position: relative;
    display: inline-block;
    margin: 0
}

.site-nav__item li {
    display: block;
    margin: 0
}

.site-nav__item .icon-chevron-down {
    width: var(--desktop-menu-chevron-size);
    height: var(--desktop-menu-chevron-size)
}

.site-nav--is-megamenu.site-nav__item {
    position: static
}

.megamenu__wrapper {
    display: flex
}

.megamenu__featured {
    flex: 0 0 19%;
    padding-bottom: 40px
}

[data-grid-style='simple'] .megamenu__featured .grid-product {
    border: 1px solid;
    border-color: var(--color-border)
}

.megamenu__cols {
    flex: 1 1 81%;
    flex-flow: column wrap;
    columns: 4
}

.megamenu--collections .megamenu__cols {
    display: flex;
    flex: 1;
    flex-flow: row;
    gap: 20px;
    columns: auto auto;
    padding-bottom: 40px
}

.megamenu__cols a:hover {
    background-color: var(--color-body);
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.megamenu__col {
    padding: 0 40px 40px 0;
    page-break-inside: avoid;
    break-inside: avoid
}

.megamenu--collections .megamenu__col {
    padding: 0;
    flex-basis: 210px
}

.megamenu__col:has([data-image-type]) {
    display: grid;
    grid-template-columns: 220px 1fr;
    gap: 10px;
    flex-basis: 440px
}

.megamenu__col:has([data-image-type]) [data-image-type] {
    width: 100%
}

.megamenu__col-title a {
    font-weight: var(--type-header-weight);
    font-size: calc(var(--type-base-size) + 1px)
}

.text-center .megamenu .float-grid {
    text-align: center
}

.text-center .megamenu .float-grid .grid__item {
    float: none;
    display: inline-block;
    vertical-align: top;
    text-align: left
}

.site-nav__dropdown-link {
    display: block;
    padding: 8px 15px;
    white-space: nowrap;
    font-size: calc(var(--type-base-size) - 1px)
}

.site-nav__dropdown-link:hover {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.megamenu .site-nav__dropdown-link {
    padding: 1px 0;
    white-space: normal
}

.site-nav__link--underline {
    position: relative
}

.site-nav__link--underline:after {
    content: '';
    display: none;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 var(--site-nav-item-padding);
    border-bottom: 2px solid;
    border-bottom-color: var(--color-nav-text)
}

.is-light .site-nav__item:not(.site-nav--has-dropdown) .site-nav__link--underline:after {
    border-bottom-color: #fff
}

.site-nav__item:hover .site-nav__link--underline:after {
    display: block
}

.site-nav--has-dropdown.is-focused>a,
.site-nav--has-dropdown:hover>a {
    color: var(--color-text-body) !important;
    background-color: var(--color-body);
    opacity: 1;
    transition: none
}

.site-nav__dropdown {
    display: block;
    visibility: hidden;
    position: absolute;
    left: 0;
    z-index: var(--z-index-header-submenu);
    background-color: var(--color-body);
    min-width: 100%;
    padding: calc(var(--gutter) / 3) 0 5px;
    box-shadow: 0 10px 20px rgb(0 0 0 / .09)
}

details[open] .site-nav__dropdown {
    visibility: visible
}

.site-nav__dropdown-list {
    margin: 0
}

.site-nav__dropdown-animate {
    transform: translateY(-10px);
    opacity: 0
}

details[open] .site-nav__dropdown-animate {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease 0.05s, transform 0.25s cubic-bezier(.165, .84, .44, 1) 0.05s
}

.site-nav__deep-dropdown {
    background-color: var(--color-body);
    box-shadow: 0 10px 20px rgb(0 0 0 / .09);
    position: absolute;
    top: 0;
    left: 100%;
    margin: 0;
    visibility: hidden;
    opacity: 0;
    z-index: var(--z-index-header);
    transform: translate3d(-12px, 0, 0)
}

.site-nav__deep-dropdown-trigger:hover .site-nav__deep-dropdown,
.is-focused+.site-nav__deep-dropdown {
    visibility: visible;
    opacity: 1;
    transform: translate3d(0, 0, 0);
    transition: all 300ms cubic-bezier(.2, .06, .05, .95)
}

.site-nav__deep-dropdown:before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 10px;
    background-image: linear-gradient(to right, rgb(0 0 0 / .09), transparent);
    pointer-events: none
}

.site-nav__deep-dropdown-trigger:hover .site-nav__dropdown-link--has-children {
    background-color: var(--color-body-dim)
}

.site-nav__dropdown-link--has-children:hover,
.site-nav__dropdown-link--has-children:focus {
    background-color: var(--color-body-dim)
}

.site-nav__deep-dropdown-trigger .icon-chevron-down {
    position: absolute;
    top: 50%;
    right: 10px;
    width: var(--desktop-menu-chevron-size);
    height: var(--desktop-menu-chevron-size);
    transform: rotate(-90deg) translateX(50%)
}

.site-nav__details {
    cursor: pointer;
    color: var(--color-nav-text)
}

.site-header__drawer {
    display: none;
    position: absolute;
    top: 1px;
    padding: 20px;
    width: 100%;
    max-height: 75vh;
    max-height: var(--max-drawer-height);
    overflow-y: auto;
    background-color: var(--color-body);
    color: var(--color-text-body);
    box-shadow: var(--drawer-box-shadow);
    z-index: var(--z-index-header-drawers);
    transition: all var(--slide-curve);
    transform: translateY(-100%)
}

@media only screen and (max-width:768px) {
    .site-header__drawer {
        top: 0;
        z-index: var(--z-index-header-drawers-mobile)
    }
}

.site-header__drawer.is-active {
    display: block;
    transform: translateY(0)
}

.header-wrapper--compressed .site-header__drawer {
    top: 0
}

.site-header__drawer-animate {
    transform: translateY(-20px);
    opacity: 0
}

.is-active .site-header__drawer-animate {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease 0.15s, transform 0.25s cubic-bezier(.165, .84, .44, 1) 0.15s
}

.site-nav__close-cart {
    display: none
}

.cart-open .site-nav__close-cart {
    display: block
}

.site-nav__icon-label {
    margin-left: 15px
}

.cart-link {
    position: relative;
    display: inline-block
}

.cart-link__bubble {
    display: none;
    position: absolute;
    top: 50%;
    right: -4px;
    font-size: 12px;
    line-height: 1;
    font-weight: var(--type-header-weight);
    letter-spacing: 0;
    text-align: center
}

.cart-link__bubble:before {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: var(--color-cart-dot);
    border-radius: 50px;
    width: 150%;
    height: 0;
    padding: 10px;
    transform: translate(-50%, -50%)
}

[data-icon='cart'] .cart-link__bubble {
    top: -2px;
    right: 0
}

.cart-link__bubble-num {
    position: relative;
    color: var(--color-cart-dot-text)
}

.cart-link__bubble--visible {
    display: block
}

.slide-nav__wrapper {
    --z-index-overlay: 1;
    position: relative;
    overflow: hidden;
    display: block
}

.slide-nav__wrapper:after {
    background-color: unset;
    pointer-events: none;
    border-radius: var(--roundness)
}

.slide-nav {
    margin: 0;
    list-style: none;
    transition: transform var(--slide-curve)
}

[data-level='2'] .slide-nav {
    transform: translateX(-100%)
}

[data-level='3'] .slide-nav {
    transform: translateX(-200%)
}

.slide-nav__button {
    display: block;
    background: none;
    border: 0;
    padding: 0;
    width: 100%;
    text-align: left
}

[dir='rtl'] .slide-nav__button {
    text-align: right
}

.slide-nav__link {
    position: relative;
    display: flex;
    width: 100%;
    padding: 10px 20px;
    align-items: center;
    justify-content: space-between;
    font-size: calc(var(--type-base-size) + 2px)
}

.slide-nav__link>span {
    display: block;
    flex: 1 1 auto
}

.slide-nav__link .icon {
    width: 11px;
    height: 11px;
    margin-left: 10px
}

.slide-nav__image {
    position: absolute;
    width: 40px;
    height: 40px;
    left: 15px;
    top: 50%;
    margin-top: -20px
}

.slide-nav__image img {
    object-fit: cover;
    width: 100%;
    height: 100%
}

.slide-nav__image+span {
    padding-left: 45px
}

.slide-nav__link--back {
    font-weight: var(--type-header-weight);
    justify-content: flex-start
}

.slide-nav__link--back>span {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.slide-nav__link--back .icon {
    margin-right: 15px;
    margin-left: 0;
    transform: rotate(180deg)
}

.slide-nav__item {
    display: block;
    width: 100%;
    margin: 0
}

.slide-nav__item:first-child {
    padding-top: 10px
}

.slide-nav__item:last-child {
    padding-bottom: 10px
}

.slide-nav__dropdown {
    display: none;
    visibility: hidden;
    position: absolute;
    width: 100%;
    top: 0;
    right: -100%;
    margin: 0;
    opacity: 0;
    transition: all var(--slide-curve)
}

.slide-nav__dropdown.is-active {
    display: block;
    visibility: visible;
    opacity: 1;
    transition: all 0.55s cubic-bezier(.165, .84, .44, 1) 0.1s forwards
}

.site-header__mobile-nav .footer__section--menus {
    margin-top: 20px;
    margin-left: calc(var(--page-width-padding) * -1);
    margin-right: calc(var(--page-width-padding) * -1)
}

header-nav {
    display: block
}

.site-header__search-container {
    display: none;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: var(--color-nav);
    color: var(--color-nav-text);
    z-index: var(--z-index-header-search)
}

.site-header__search-container.is-active {
    display: flex
}

.site-header__search-container .page-width {
    display: flex;
    width: 100%;
    position: relative
}

.site-header__search {
    display: flex;
    width: 100%
}

.site-header__search .icon {
    width: 26px;
    height: 26px
}

.site-header__search>is-land {
    width: 100%
}

svg.icon {
    display: inline-block;
    vertical-align: middle;
    width: var(--icon-width-size, var(--icon-size, 20px));
    height: var(--icon-height-size, var(--icon-size, 20px))
}

svg.icon.icon--line {
    stroke-width: var(--icon-stroke-width, 2px);
    stroke-linejoin: var(--icon-stroke-line-join, miter)
}

.collection-grid__wrapper.unload {
    min-height: 180px;
    position: relative
}

@media only screen and (min-width:769px) {
    .collection-sidebar {
        padding-right: 10px;
        margin-bottom: 20px
    }
}

.collection-grid {
    margin-bottom: var(--index-section-padding)
}

@media only screen and (max-width:768px) {

    [data-grid-style='simple'] .collection-grid,
    [data-grid-style*='grey'] .collection-grid,
    [data-grid-style*='white'] .collection-grid {
        padding-top: 10px
    }
}

.unload [data-section-type='collection-template'] {
    animation: grid-product__loading 1.5s ease infinite 1.5s
}

.unload .collection-grid {
    opacity: .2;
    transition: opacity 0.3s
}

.item-grid__sidebar {
    min-height: 0;
    position: sticky;
    top: 90px;
    max-height: 90vh;
    overflow-y: auto
}

.collection-mobile-filters .collapsible-trigger__layout--inline {
    justify-content: space-between
}

@media only screen and (max-width:768px) {
    .collection-mobile-filters .collapsible-trigger-btn {
        padding-right: 17px;
        padding-left: 17px
    }

    .collection-mobile-filters .collapsible-trigger__icon {
        right: 17px
    }

    .collection-mobile-filters .filter-wrapper {
        display: none;
        transition: all var(--slide-curve);
        background-color: var(--color-body);
        transform: translateY(-100%);
        max-height: 80vh;
        max-height: var(--max-filters-height);
        overflow: auto;
        box-shadow: var(--drawer-box-shadow)
    }

    .collection-mobile-filters .filter-wrapper.is-active {
        display: block;
        transform: translateY(0)
    }

    .collection-mobile-filters {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 0;
        overflow: visible
    }
}

.collection-filter {
    margin-bottom: calc(var(--gutter) / 2)
}

@media only screen and (max-width:768px) {
    .collection-filter {
        position: sticky;
        top: 0;
        z-index: 2;
        margin-left: calc(var(--page-width-gutter-small) * -1);
        margin-right: calc(var(--page-width-gutter-small) * -1);
        margin-bottom: 0
    }
}

.collection-filter__inner {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: nowrap
}

@media only screen and (max-width:768px) {
    .collection-filter__inner {
        position: relative;
        z-index: 2;
        justify-content: space-between;
        background: var(--color-body);
        padding: 0 var(--page-width-gutter-small)
    }

    .collection-filter__inner:after {
        content: '';
        position: absolute;
        height: 20px;
        top: 100%;
        left: 0;
        right: 0;
        pointer-events: none;
        background: linear-gradient(rgb(0 0 0 / .05), #fff0)
    }
}

.collection-filter__inner select {
    display: block;
    width: 100%;
    border: 0
}

.collection-filter__btn {
    padding: 12px 0;
    font-weight: var(--type-header-weight);
    font-size: calc(var(--type-base-size) + 1px)
}

.collection-filter__btn .icon {
    vertical-align: middle;
    width: 25px;
    height: 25px;
    margin-top: -1px;
    margin-right: 5px
}

.collection-filter__btn path {
    transition: all 0.3s cubic-bezier(.18, .77, .58, 1)
}

.collection-filter__btn.is-active path:nth-child(3) {
    transform: rotate(45deg);
    transform-origin: 70% 90%
}

.collection-filter__btn.is-active path:nth-child(1),
.collection-filter__btn.is-active path:nth-child(2),
.collection-filter__btn.is-active path:nth-child(4),
.collection-filter__btn.is-active path:nth-child(5) {
    opacity: 0
}

.collection-filter__btn.is-active path:nth-child(6) {
    transform: rotate(-45deg);
    transform-origin: 80% 44%
}

.collection-filter__item {
    flex: 0 1 auto
}

.collection-filter__item--drawer {
    flex: 0 1 50%;
    padding-right: calc(var(--gutter) / 4)
}

.collection-filter__item--count {
    flex: 1 1 50%;
    text-align: center
}

@media only screen and (max-width:768px) {
    .collection-filter__item--count {
        order: 3;
        flex: 1 1 100%
    }
}

.grid-view-btn {
    display: block;
    padding: 10px 8px;
    opacity: .15
}

.grid-view-btn:hover {
    opacity: .4
}

.grid-view-btn.is-active {
    opacity: 1
}

.grid-view-btn svg {
    display: block
}

@media only screen and (max-width:768px) {
    .grid-view-btn svg {
        width: 18px;
        height: 18px
    }
}

.collection-filter__item--right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 0 1 auto
}

.collection-filter__item--right>.inline-list {
    margin-right: -7px
}

@media only screen and (max-width:768px) {
    .collection-filter__sort select {
        margin-left: -8px
    }
}

@media only screen and (min-width:769px) {
    .collection-filter__sort {
        padding-right: 15px;
        margin-right: 15px;
        border-right: 1px solid;
        border-right-color: var(--color-border)
    }
}

.image-filter__wrapper ul {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px
}

.image-filter__wrapper ul label {
    height: 100%;
    align-items: flex-start;
    border: 1px solid #fff0
}

.image-filter__wrapper ul label:active,
.image-filter__wrapper ul label:hover,
.image-filter__wrapper ul label:focus-within {
    border-color: var(--color-border)
}

.image-filter__wrapper ul .tag__text {
    margin-left: 0
}

.image-filter__wrapper .tag--active label {
    border-color: var(--color-text-body);
    border: 2px solid
}

.image-filter__image-wrapper {
    display: flex;
    flex-direction: column;
    gap: 10px;
    text-align: center
}

.tag-list--active-tags:empty {
    display: none
}

@media only screen and (max-width:768px) {
    .tag-list--active-tags {
        margin: 20px 20px 10px
    }

    .collection-sidebar__group {
        border-top: 1px solid;
        border-top-color: var(--color-border)
    }
}

.collection-sidebar__group {
    overflow: hidden
}

@media only screen and (max-width:768px) {
    .collection-sidebar__group .tag-list:not(.tag-list--swatches) {
        columns: 2
    }
}

.collection-sidebar__group .tag-list a:hover {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

@media only screen and (min-width:769px) {
    .collection-sidebar__group .collapsible-trigger-btn {
        padding-top: 5px
    }
}

:root {
    --tag-active-icon-size: 16px;
    --tag-active-icon-size-active: 10px
}

.tag-list__header {
    text-align: left
}

.tag-list .tag-list {
    margin-left: calc(var(--gutter) / 2)
}

.tag-list a,
.tag-list button,
.tag-list label {
    cursor: pointer;
    display: block;
    padding: 1px 0
}

.tag--swatch label {
    margin: 0
}

.tag-list label:hover,
.tag:focus-within label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.tag--active>a,
.tag--active>button,
.tag--active>label {
    font-weight: 900
}

.tag-list--checkboxes {
    padding-bottom: 6px
}

.tag-list--checkboxes a {
    position: relative;
    padding-left: 25px
}

.tag-list--checkboxes a:before,
.tag-list--checkboxes a:after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%)
}

.tag-list--checkboxes a:before {
    border: 1px solid;
    border-color: var(--color-border);
    height: var(--tag-active-icon-size);
    width: var(--tag-active-icon-size)
}

.tag-list--checkboxes a:after {
    height: var(--tag-active-icon-size-active);
    width: var(--tag-active-icon-size-active);
    left: 3px
}

.tag-list--checkboxes .tag--active a:after,
.tag--active .tag__checkbox:after,
input:checked~.tag__checkbox:after {
    background-color: var(--color-text-body);
    border-color: var(--color-text-body)
}

.tag--remove,
.tag--inline {
    display: inline-block;
    position: relative;
    margin: 0 10px 13px 0
}

.tag--remove a,
.tag--inline a {
    display: block;
    text-align: left;
    padding: 7px 15px 7px 36px;
    min-width: 0
}

.tag--remove .icon,
.tag--inline .icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--color-button-primary-text)
}

.tag--inline a {
    padding: 7px 15px 7px 15px
}

.tag-list--swatches {
    margin-top: 2px;
    margin-left: -2px
}

.drawer .tag-list--swatches {
    margin-left: -2px
}

.tag-list--swatches li {
    display: inline-block
}

label.tag__checkbox-wrapper {
    display: flex
}

.tag__checkbox {
    position: relative;
    padding-left: 25px;
    overflow: hidden
}

.tag__checkbox:before,
.tag__checkbox:after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%)
}

.tag__checkbox:before {
    border: 1px solid;
    border-color: var(--color-border);
    height: var(--tag-active-icon-size);
    width: var(--tag-active-icon-size)
}

.tag__checkbox:after {
    height: var(--tag-active-icon-size-active);
    width: var(--tag-active-icon-size-active);
    left: 3px
}

.tag__input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0
}

.tag--swatch {
    display: inline-block
}

.tag--swatch .color-swatch {
    width: 35px;
    height: 35px;
    flex-shrink: 0
}

.tag--active .color-swatch:hover:after {
    position: absolute;
    content: '';
    left: 50%;
    top: 0;
    bottom: 0;
    border-left: 1px solid;
    border-color: var(--color-border);
    transform: rotate(45deg)
}

.tag:not(.tag--active) label:hover .color-swatch:hover,
.tag:focus-within .color-swatch {
    box-shadow: 0 0 0 1px var(--color-text-body)
}

.tag--active .color-swatch {
    box-shadow: 0 0 0 2px var(--color-text-body)
}

.tag--show-label {
    width: 100%
}

.tag--show-label label {
    display: flex;
    align-items: center
}

.tag--show-label .tag__text {
    display: block !important;
    margin-left: .5rem;
    margin-bottom: 4px
}

.map {
    display: block;
    height: 100%;
    position: relative
}

.map__image {
    width: 100%;
    height: 100%;
    object-fit: cover
}

.map__onboarding {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    background-size: cover;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    z-index: 0
}

.product-image-main {
    position: relative
}

.product-image-main[data-size] img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain
}

.product-image-main[data-size='square'] .image-wrap {
    padding-bottom: 100% !important
}

.product-image-main[data-size='landscape'] .image-wrap {
    padding-bottom: 75% !important
}

.product-image-main[data-size='portrait'] .image-wrap {
    padding-bottom: 150% !important
}

.product__model-wrapper model-viewer {
    width: 100%;
    height: 100%
}

.product__video-wrapper {
    background-color: var(--color-small-image-bg)
}

.product__video-wrapper.loaded[data-video-style='muted']:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1
}

.product__video-wrapper.video-interactable:before {
    display: none
}

.product__video-wrapper.loaded:after {
    display: none
}

.product__video-wrapper.loading iframe {
    opacity: .01
}

.product__video-wrapper.loaded iframe {
    opacity: 1
}

.product__video-wrapper__grippy {
    position: absolute;
    left: 0;
    width: calc(50% - 50px);
    top: 32px;
    bottom: 40px;
    z-index: 3
}

.product__video-wrapper__grippy:before {
    position: absolute;
    display: block;
    content: '';
    width: 60%;
    height: 35%;
    top: 0;
    left: 100%
}

.product__video-wrapper__grippy+.product__video-wrapper__grippy {
    left: auto;
    right: 0
}

.product__video-wrapper__grippy+.product__video-wrapper__grippy:before {
    bottom: 0;
    top: auto;
    right: 100%;
    left: auto
}

.product-video-trigger {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%)
}

.product__photo-zoom {
    position: absolute !important;
    bottom: 0;
    right: 0;
    cursor: zoom-in;
    border: 0
}

@media only screen and (max-width:768px) {
    .product__photo-zoom {
        box-shadow: 0 3px 6px rgb(0 0 0 / .15);
        padding: 8px
    }

    .product__photo-zoom .icon {
        width: 21px;
        height: 21px
    }

    .product__main-photos .product__photo-zoom {
        margin-bottom: 10px;
        margin-right: 10px
    }

    .product-slideshow .product__photo-zoom {
        opacity: 0;
        transition: opacity 0.5s ease-out
    }

    .product-slideshow .is-selected .product__photo-zoom {
        opacity: 1
    }
}

@media only screen and (min-width:769px) {
    .product__photo-zoom {
        opacity: 0;
        width: 100%;
        top: 0;
        left: 0;
        margin: 0;
        border-radius: 0
    }

    .product__photo-zoom svg,
    .product__photo-zoom span {
        display: none
    }
}

.product-single__close-media {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2
}

.multi-selectors {
    display: flex;
    justify-content: center;
    flex-wrap: wrap
}

.multi-selectors__item {
    margin: 0 20px
}

.toolbar .multi-selectors__item {
    margin-right: 0;
    margin-bottom: 0
}

.multi-selectors--footer {
    justify-content: flex-start
}

.multi-selectors--footer .multi-selectors__item {
    margin-bottom: 20px;
    margin-left: 0
}

.currency-flag {
    position: relative;
    display: inline-block;
    width: 22px;
    height: 22px;
    object-fit: cover;
    vertical-align: middle;
    overflow: hidden;
    box-shadow: 0 0 1px 0 rgb(0 0 0 / .3) inset;
    border-radius: 50%;
    background-clip: padding-box
}

@media only screen and (max-width:768px) {
    [data-disclosure-currency] .disclosure-list {
        left: 50%;
        transform: translateX(-50%);
        max-width: 90vw
    }

    [data-disclosure-currency] .disclosure-list--single-true {
        left: 0;
        transform: none
    }
}

:root {
    --max-height-disclosure: 60vh;
    --min-height-disclosure: 92px
}

.disclosure {
    position: relative
}

.disclosure__toggle {
    white-space: nowrap
}

.disclosure-list {
    background-color: var(--color-body);
    color: var(--color-text-body);
    bottom: 100%;
    padding: 10px 0;
    margin: 0;
    position: absolute;
    display: none;
    min-height: var(--min-height-disclosure);
    max-height: var(--max-height-disclosure);
    overflow-y: auto;
    border-radius: var(--input-radius);
    box-shadow: 0 0 20px rgb(0 0 0 / .09);
    z-index: 1
}

.disclosure-list a,
.disclosure-list .disclosure-list__item a {
    color: currentColor
}

.disclosure-list--down {
    bottom: auto;
    top: 100%
}

.disclosure-list--left {
    right: 0
}

.disclosure-list--visible {
    display: block
}

.disclosure-list__item {
    white-space: nowrap;
    padding: 5px 15px 4px;
    text-align: left
}

.disclosure-list__option:focus .disclosure-list__label,
.disclosure-list__option:hover .disclosure-list__label {
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.disclosure-list__item--current .disclosure-list__label {
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.disclosure-list__label {
    display: inline-block;
    vertical-align: middle;
    text-underline-offset: .2rem
}

[data-disclosure-currency] .disclosure-list__label {
    padding-left: 10px
}

.disclosure-list__label span {
    border-bottom: 2px solid #fff0
}

.is-active .disclosure-list__label span {
    border-bottom: 2px solid currentColor
}

.newsletter__input-group {
    margin: 0 auto 20px;
    max-width: 400px;
    gap: var(--input-button-gap)
}

.newsletter__input-group:last-of-type {
    margin-bottom: 0
}

.newsletter__input::-webkit-input-placeholder {
    color: var(--color-text-body);
    opacity: 1
}

.newsletter__input:-moz-placeholder {
    color: var(--color-text-body);
    opacity: 1
}

.newsletter__input::-moz-placeholder {
    color: var(--color-text-body);
    opacity: 1
}

.newsletter__input:-ms-input-placeholder {
    color: var(--color-text-body)
}

.newsletter__input::-ms-input-placeholder {
    color: var(--color-text-body);
    opacity: 1
}

.form__submit--small {
    line-height: 0
}

@media only screen and (max-width:768px) {
    .form__submit--large {
        display: none
    }

    .form__submit--small {
        display: block
    }
}

@media only screen and (min-width:769px) {
    .form__submit--large {
        display: block
    }

    .form__submit--small {
        display: none
    }
}

.shopify-challenge__container {
    padding: 30px 22px
}

@media only screen and (min-width:769px) {
    .shopify-challenge__container {
        padding: 120px 0
    }
}

.input-group {
    display: flex
}

.input-group .input-group-field:first-child,
.input-group .input-group-btn:first-child .btn,
.input-group input[type='hidden']:first-child+.input-group-field {
    border-radius: var(--input-radius) 0 0 var(--input-radius)
}

.input-group .input-group-field:last-child {
    border-radius: 0 var(--input-radius) var(--input-radius) 0
}

.input-group .input-group-btn:first-child .btn,
.input-group input[type='hidden']:first-child+.input-group-btn .btn {
    border-radius: var(--button-radius) 0 0 var(--button-radius)
}

[dir='rtl'] .input-group .input-group-btn:first-child .btn,
[dir='rtl'] .input-group input[type='hidden']:first-child+.input-group-btn .btn {
    border-radius: 0 var(--button-radius) var(--button-radius) 0
}

.input-group .input-group-btn:last-child .btn {
    border-radius: 0 var(--button-radius) var(--button-radius) 0
}

[dir='rtl'] .input-group .input-group-btn:last-child .btn {
    border-radius: var(--button-radius) 0 0 var(--button-radius)
}

.input-group input::-moz-focus-inner {
    border: 0;
    padding: 0;
    margin-top: -1px;
    margin-bottom: -1px
}

.input-group-field {
    flex: 1 1 auto;
    margin: 0;
    min-width: 0
}

.input-group-btn {
    flex: 0 1 auto;
    margin: 0;
    display: flex
}

.input-group-btn .icon {
    vertical-align: initial
}

.input-group-btn .btn {
    min-width: 0
}

newsletter-reminder {
    position: fixed;
    left: 20px;
    bottom: 20px;
    transition: ease-in-out 0.3s opacity;
    box-shadow: 0 12px 25px rgb(0 0 0 / .15);
    max-width: 240px;
    z-index: 10
}

@media only screen and (max-width:768px) {
    newsletter-reminder {
        max-width: calc(100% - 40px)
    }
}

newsletter-reminder[data-enabled='false'] {
    opacity: 0;
    visibility: hidden;
    pointer-events: none
}

newsletter-reminder[class*='color-scheme-']:not(.color-scheme-none) {
    position: fixed
}

newsletter-reminder .color-scheme-none {
    color: var(--color-button-primary-text);
    background-color: var(--color-button-primary)
}

.newsletter-reminder__message.h3 {
    cursor: pointer;
    padding: var(--newsletter-reminder-padding);
    margin: 0
}

.pagination {
    width: 100%;
    margin: calc(var(--gutter) * 2) 0;
    text-align: center
}

.pagination>span {
    display: inline-block;
    vertical-align: middle;
    line-height: 1
}

.pagination a {
    display: inline-block
}

.pagination a:not(.btn),
.pagination .page.current {
    padding: 8px 12px
}

.pagination .page.current {
    opacity: .3
}

.pagination .btn {
    transition: transform 0.15s ease-out
}

.pagination .btn:hover {
    transform: scale(1.08)
}

.pagination .btn .icon {
    width: 13px;
    height: 13px
}

.parallax-image img {
    width: 100%;
    height: 100%;
    object-fit: cover
}

.parallax-image {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    transition: transform 0.05s linear
}

.pswp {
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    overflow: hidden;
    touch-action: none;
    z-index: 15000;
    -webkit-text-size-adjust: 100%;
    -webkit-backface-visibility: hidden;
    outline: none
}

.pswp img {
    max-width: none
}

.pswp--animate_opacity {
    opacity: .001;
    will-change: opacity;
    transition: opacity 333ms cubic-bezier(.4, 0, .22, 1)
}

.pswp--open {
    display: block
}

.pswp--zoom-allowed .pswp__img {
    cursor: zoom-in
}

.pswp--zoomed-in .pswp__img {
    cursor: grab
}

.pswp--dragging .pswp__img {
    cursor: grabbing
}

.pswp__bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--color-body);
    opacity: 0;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    will-change: opacity
}

.pswp__scroll-wrap {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.pswp__container,
.pswp__zoom-wrap {
    touch-action: none;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0
}

.pswp__container,
.pswp__img {
    -webkit-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: #fff0;
    -webkit-touch-callout: none
}

.pswp__zoom-wrap {
    position: absolute;
    width: 100%;
    transform-origin: left top;
    transition: transform 333ms cubic-bezier(.4, 0, .22, 1)
}

.pswp__bg {
    will-change: opacity;
    transition: opacity 333ms cubic-bezier(.4, 0, .22, 1)
}

.pswp--animated-in .pswp__bg,
.pswp--animated-in .pswp__zoom-wrap {
    transition: none
}

.pswp__container,
.pswp__zoom-wrap {
    -webkit-backface-visibility: hidden
}

.pswp__item {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    overflow: hidden
}

.pswp__img {
    position: absolute;
    width: auto;
    height: auto;
    top: 0;
    left: 0
}

.pswp__img--placeholder {
    -webkit-backface-visibility: hidden
}

.pswp--ie .pswp__img {
    width: 100% !important;
    height: auto !important;
    left: 0;
    top: 0
}

.pswp__error-msg {
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    text-align: center;
    line-height: 16px;
    margin-top: -8px;
    color: #ccc
}

.pswp__error-msg a {
    color: #ccc;
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.pswp__button {
    position: relative
}

.pswp__button:after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.pswp__button svg {
    pointer-events: none
}

.pswp__button--arrow--left .icon,
.pswp__button--arrow--right .icon {
    width: 13px;
    height: 13px;
    margin: 8px
}

.pswp__button[disabled] {
    opacity: 0;
    pointer-events: none
}

.pswp__ui {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    bottom: var(--gutter);
    left: 0;
    right: 0;
    transform: translateY(0);
    transition: transform 0.25s 0.6s
}

.pswp__ui .btn {
    margin: 15px;
    border: 0;
    box-shadow: 0 3px 6px rgb(0 0 0 / .15)
}

.pswp__ui--hidden {
    transform: translateY(150%);
    transition: transform 0.25s
}

html.pswp-open-in-ios,
html.pswp-open-in-ios body {
    background: #444;
    height: var(--window-inner-height);
    overflow: hidden;
    box-sizing: border-box
}

.pswp-open-in-ios body>* {
    display: none
}

.pswp-open-in-ios body .pswp.pswp--open {
    display: block
}

.placeholder-noblocks {
    padding: 40px;
    text-align: center
}

.placeholder-svg {
    fill: #999;
    background-color: #e1e1e1;
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 100%;
    display: block;
    padding: 30px 0
}

.placeholder-svg--no-padding {
    padding: 0
}

predictive-search {
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
    max-width: 960px;
    margin: 0 auto
}

predictive-search.is-active {
    flex: 99
}

predictive-search.is-active .predictive__screen {
    display: block;
    z-index: 3
}

predictive-search.is-active .btn--close-search {
    display: flex;
    align-items: center;
    justify-content: center
}

.site-header__search-island {
    display: block;
    width: 100%
}

.site-header__search-results {
    position: absolute;
    top: 100%;
    transform: translateY(var(--header-padding-bottom));
    left: 0;
    right: 0;
    background-color: var(--color-body);
    color: var(--color-text-body);
    max-height: 70vh;
    max-height: calc(90vh - 100%);
    overflow: auto;
    box-shadow: 0 10px 20px rgb(0 0 0 / .09);
    transition: transform 0.3s cubic-bezier(.18, .77, .58, 1)
}

.header-wrapper--compressed .site-header__search-results {
    transform: translateY(0)
}

.site-header__search-results-wrapper {
    padding: 15px 0
}

@media only screen and (min-width:769px) {
    .site-header__search-results {
        max-height: calc(100vh - 100% - 33px)
    }

    .site-header__search-results-wrapper {
        padding: 30px 0
    }
}

.predictive__label {
    margin-top: 20px;
    border-bottom: 1px solid;
    border-bottom-color: var(--color-border);
    padding-bottom: 5px;
    margin-bottom: 20px
}

:root {
    --predictive-gap: 10px
}

.predictive-result__layout {
    display: flex;
    flex-wrap: wrap;
    padding: var(--predictive-gap);
    margin-left: calc(var(--predictive-gap) * -1);
    margin-right: calc(var(--predictive-gap) * -1)
}

.predictive-result__layout>div {
    margin: 0 var(--predictive-gap) var(--gutter)
}

.predictive-result__layout>div:last-child {
    margin-bottom: 0
}

.predictive-result__layout [data-type-products] {
    flex: 1 1 60%;
    margin-bottom: 0
}

.predictive-result__layout [data-type-products] img {
    object-fit: contain
}

.predictive-result__layout [data-type-products] .predictive-image-fill-space--true img {
    object-fit: cover
}

.predictive-result__layout [data-type-collections],
.predictive-result__layout [data-type-pages] {
    flex: 1 1 200px
}

.predictive-result__layout [data-type-articles] {
    flex: 1 1 60%
}

.predictive-result__layout [data-type-articles] .grid-item__meta {
    margin-left: 10px
}

.predictive__image-wrap {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.predictive__image-wrap img {
    object-position: 50% 0
}

.predictive-results__footer {
    padding: 0 0 30px
}

.predictive-overflow-hidden {
    overflow: hidden
}

@media only screen and (max-width:768px) {
    .predictive-overflow-hidden {
        overflow: auto
    }
}

[data-dark='true'] input {
    color: #fff !important
}

[data-dark='true'] .btn--search path {
    stroke: #fff !important
}

[data-dark='false'] input {
    color: #000 !important
}

[data-dark='false'] .btn--search path {
    stroke: #000 !important
}

.predictive__screen {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    background: var(--color-modal-bg);
    display: none
}

predictive-search form {
    display: flex;
    flex-wrap: wrap;
    z-index: 4;
    position: relative;
    width: 100%
}

.search__input-wrap {
    position: relative;
    flex: 5;
    transition: flex 0.3s ease-in-out
}

.search__input {
    background: var(--color-nav-search, #fff);
    color: var(--color-body-text);
    flex: 1;
    width: 100%;
    border-radius: calc(var(--roundness, 0) * 2);
    padding-left: 16px;
    padding-right: 16px;
    font-size: 16px
}

.btn--search {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 15px
}

.btn--search .icon {
    height: 25px;
    width: 25px
}

@media only screen and (min-width:769px) {
    .btn--search {
        right: 10px
    }
}

[dir='rtl'] .btn--search {
    right: auto;
    left: 15px
}

@media only screen and (min-width:769px) {
    [dir='rtl'] .btn--search {
        left: 10px
    }
}

.btn--close-search {
    display: none;
    flex: 30px 0 0;
    margin-left: 10px
}

.btn--close-search .icon {
    height: 25px;
    width: 25px
}

.btn--close-search .icon path {
    stroke: var(--color-nav-text) !important
}

.search__results {
    background: var(--color-body);
    color: var(--color-body-text);
    position: absolute;
    width: 100%;
    min-width: 400px;
    top: 100%;
    padding: 0;
    display: none;
    margin-top: 10px;
    container-type: inline-size;
    max-height: 80vh;
    overflow-y: auto;
    word-break: break-all
}

@media only screen and (max-width:768px) {
    .search__results {
        width: calc(100% + var(--page-width-padding, 17px) * 2);
        margin-left: calc(var(--page-width-padding, 17px) * -1);
        margin-right: calc(var(--page-width-padding, 17px) * -1);
        max-height: 75vh
    }

    .template-search .page-content .search__results {
        max-height: 50vh
    }
}

.price-range__slider-wrapper {
    padding: 0 8px;
    margin-bottom: 8px
}

@media only screen and (max-width:768px) {
    .price-range__slider-wrapper {
        padding: 0 10px
    }
}

.price-range__input {
    display: none
}

.price-range__display-wrapper {
    display: flex;
    flex: 1 1 auto;
    justify-content: space-between;
    padding: 0;
    margin-bottom: 8px;
    width: 100%
}

@media only screen and (max-width:768px) {
    .price-range__display-wrapper {
        padding: 0
    }
}

.noUi-horizontal .noUi-handle {
    border: 0;
    border-radius: 50%;
    background: var(--color-text-body);
    box-shadow: 0 0 1px 2px #fff;
    width: 12px;
    height: 12px;
    cursor: pointer;
    right: -6px;
    top: -3px
}

.noUi-horizontal .noUi-handle:hover,
.noUi-horizontal .noUi-handle:focus {
    width: 14px;
    height: 14px;
    right: -7px;
    top: -4px
}

.noUi-horizontal .noUi-handle:before,
.noUi-horizontal .noUi-handle:after {
    content: none
}

.noUi-target {
    background: #f4f4f4;
    border: 0;
    box-shadow: none
}

.noUi-connect {
    background: var(--color-text-body)
}

.noUi-horizontal {
    height: 6px
}

product-recommendations[data-intent='complementary'] {
    display: block;
    overflow: hidden
}

product-recommendations[data-intent='complementary'] .product-recommendations__title {
    margin-top: 40px;
    width: 100%;
    max-width: 72%;
    margin-bottom: 20px;
    text-align: left
}

product-recommendations[data-intent='complementary'] .float-grid {
    margin-left: 0
}

product-recommendations[data-intent='complementary'] .grid__item,
product-recommendations[data-intent='complementary'] .grid-item {
    margin-bottom: 1rem;
    padding-left: 0;
    padding-bottom: 8px
}

@media only screen and (max-width:768px) {

    product-recommendations[data-intent='complementary'] .grid__item,
    product-recommendations[data-intent='complementary'] .grid-item {
        margin-left: 0 !important;
        flex: 0 0 100%
    }
}

product-recommendations[data-intent='complementary'] .grid-product__image-wrap {
    min-width: 90px
}

product-recommendations[data-intent='complementary'] .grid__image-ratio {
    background-color: #fff0
}

product-recommendations[data-intent='complementary'] img.image-style--circle {
    border-radius: 50%;
    object-fit: cover;
    width: 85%;
    height: 85%;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    margin: auto
}

product-recommendations[data-intent='complementary'] .grid__item-image-wrapper .grid-product__link {
    width: 100%;
    display: block
}

product-recommendations[data-intent='complementary'] .grid__item-image-wrapper,
product-recommendations[data-intent='complementary'] .grid-product__link,
product-recommendations[data-intent='complementary'] .grid-item__link {
    display: flex;
    align-items: center
}

product-recommendations[data-intent='complementary'] .grid__item-image-wrapper .grid-product__image-mask,
product-recommendations[data-intent='complementary'] .grid-product__link .grid-product__image-mask,
product-recommendations[data-intent='complementary'] .grid-item__link .grid-product__image-mask,
product-recommendations[data-intent='complementary'] .grid__item-image-wrapper .grid-product__image-wrap,
product-recommendations[data-intent='complementary'] .grid-product__link .grid-product__image-wrap,
product-recommendations[data-intent='complementary'] .grid-item__link .grid-product__image-wrap,
product-recommendations[data-intent='complementary'] .grid__item-image-wrapper .image-wrap,
product-recommendations[data-intent='complementary'] .grid-product__link .image-wrap,
product-recommendations[data-intent='complementary'] .grid-item__link .image-wrap {
    width: 112px;
    margin: 0
}

product-recommendations[data-intent='complementary'] .grid__item-image-wrapper .grid-product__meta,
product-recommendations[data-intent='complementary'] .grid-product__link .grid-product__meta,
product-recommendations[data-intent='complementary'] .grid-item__link .grid-product__meta {
    margin-left: 48px;
    width: calc(100% - 112px);
    text-align: left
}

product-recommendations[data-intent='complementary'] .grid-product__actions {
    top: 0;
    right: 0
}

product-recommendations[data-intent='complementary'] .grid-product__tag {
    top: initial;
    right: initial;
    left: 0;
    bottom: 0
}

product-recommendations[data-intent='complementary'] .flickity-viewport {
    width: 100%
}

product-recommendations[data-intent='complementary'] .flickity-previous {
    left: auto;
    top: -32px;
    right: 50px
}

@media only screen and (max-width:768px) {
    product-recommendations[data-intent='complementary'] .flickity-previous {
        right: 45px
    }
}

product-recommendations[data-intent='complementary'] .flickity-next {
    right: 0;
    top: -32px
}

product-recommendations[data-intent='complementary'] .flickity-page-dots {
    top: -41px;
    right: 0;
    width: auto;
    bottom: auto
}

product-recommendations[data-intent='complementary'] .flickity-button:disabled {
    display: block;
    opacity: .35
}

.product-recommendations__slide {
    display: block;
    overflow: hidden;
    width: 100%;
    margin: 0;
    padding: 0
}

@media only screen and (max-width:768px) {
    .product-recommendations__slide {
        display: flex;
        flex-wrap: wrap
    }
}

.product-recommendations__slide .grid__item:last-child {
    margin-bottom: 0;
    padding-bottom: 0
}

.product-recommendations--title-missing {
    margin-top: 80px
}

product-recommendations[data-intent='complementary'] .grid-product__actions {
    top: 0;
    right: 5px
}

@media only screen and (min-width:769px) {
    product-recommendations[data-intent='complementary'] .grid-product__actions .btn--icon {
        padding: 10px
    }

    product-recommendations[data-intent='complementary'] .grid-product__actions .icon {
        width: 25px;
        height: 25px
    }
}

product-recommendations[data-intent='complementary'] .grid-product__tags {
    margin-left: 0
}

product-recommendations[data-intent='complementary'] .grid-item {
    padding: 8px
}

[data-grid-style*='grey'] product-recommendations[data-intent='complementary'] .grid-item__content,
[data-grid-style*='grey-round'] product-recommendations[data-intent='complementary'] .grid-item__content,
[data-grid-style*='white'] product-recommendations[data-intent='complementary'] .grid-item__content,
[data-grid-style*='white-round'] product-recommendations[data-intent='complementary'] .grid-item__content {
    padding: 0 8px
}

@media only screen and (max-width:768px) {
    [data-grid-style*='gridlines'] product-recommendations[data-intent='complementary'] .grid-product__actions {
        top: -6px;
        right: -6px
    }
}

[data-grid-style*='gridlines'] product-recommendations[data-intent='complementary'] .product-recommendations__slide .grid-product:last-child:after {
    border-top: 0
}

[data-grid-style*='gridlines'] product-recommendations[data-intent='complementary'] .product-recommendations__slide .grid-product:first-child:after {
    border-top: solid var(--grid-thickness) var(--color-border)
}

[data-grid-style*='gridlines'] product-recommendations[data-intent='complementary'] .grid-product {
    padding: 8px;
    margin-bottom: 0
}

[data-grid-style*='gridlines'] product-recommendations[data-intent='complementary'] .grid-product:after {
    padding: 0 8px;
    box-shadow: none;
    border: solid var(--grid-thickness) var(--color-border)
}

.template-product .grid-product--prefix {
    margin-top: 16px;
    margin-bottom: 16px
}

.product-grid-item {
    width: 100%;
    background-color: var(--color-body);
    container: product-grid-item / inline-size
}

.grid-product__colors {
    display: flex;
    flex-wrap: wrap;
    line-height: 25px;
    margin: 0 0 5px -4px
}

.grid-product__colors+.grid-product__colors {
    margin-top: 4px
}

.grid-product__colors .color-swatch {
    width: 25px;
    height: 25px;
    border-width: 2px
}

.color-swatch__more {
    line-height: 25px;
    margin-left: 5px;
    font-weight: var(--type-header-weight)
}

@media only screen and (min-width:769px) {
    .product-grid[data-view='list'] .product-tile-layout--inline .grid-product__price--current {
        font-size: calc(var(--type-base-size) + 2px)
    }
}

@media only screen and (max-width:959px) {
    .product-tile-layout--inline.grid-item {
        flex: 0 0 100%
    }
}

@media only screen and (max-width:959px) {
    .scrollable-grid--small .product-tile-layout--inline.grid-item {
        flex: 0 0 85%
    }
}

.product-tile-layout--inline .grid-item__link {
    display: flex;
    gap: 32px;
    padding: 16px 16px 8px 16px
}

@media only screen and (min-width:769px) {
    .product-tile-layout--inline .grid-item__link {
        padding: 20px
    }
}

.product-tile-layout--inline .grid-product__image-wrap {
    width: 100%;
    margin: calc(var(--product-tile-margin) / 2) calc(var(--product-tile-margin) / 2) 0
}

.product-tile-layout--inline .grid-item__meta {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 0 0 35%;
    margin: 0
}

@media only screen and (min-width:769px) {
    [data-view='small'] .product-tile-layout--inline .grid-item__meta {
        justify-content: flex-start;
        margin-top: 20px
    }
}

.product-tile-layout--inline .grid-item__meta-main {
    display: flex;
    flex-direction: column;
    justify-content: center
}

.product-tile-layout--inline .grid-item__meta-main .grid-product__colors {
    margin-top: 5px
}

.product-tile-layout--inline .grid-item__meta-secondary {
    flex: initial
}

[data-view='list'] .product-tile-layout--inline .grid-item__meta-secondary {
    text-align: left
}

[data-grid-style*='gridlines'] .product-grid {
    margin-left: 0;
    margin-right: 0
}

[data-grid-style*='gridlines'] .grid-product {
    position: relative;
    padding: 0;
    margin: 0
}

[data-grid-style*='gridlines'] .grid-product:after {
    pointer-events: none;
    box-shadow: 0 0 0 var(--grid-thickness) var(--color-border)
}

@media only screen and (max-width:768px) {
    [data-grid-style*='gridlines'] .product-grid {
        margin-left: calc(var(--page-width-padding) * -1);
        margin-right: calc(var(--page-width-padding) * -1);
        padding: var(--grid-thickness) calc(var(--page-width-padding) + var(--grid-thickness))
    }

    [data-grid-style*='gridlines'] .collection-grid {
        padding: 0
    }

    [data-grid-style*='gridlines'] [data-section-id*='featured-collection'],
    [data-grid-style*='gridlines'] .index-section--sub-product {
        overflow-x: hidden
    }
}

[data-grid-style*='grey'] .grid-product,
[data-grid-style*='white'] .grid-product {
    position: relative
}

[data-grid-style*='grey'] .grid-product:after,
[data-grid-style*='white'] .grid-product:after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: calc(var(--product-grid-padding) / 2);
    right: calc(var(--product-grid-padding) / 2);
    pointer-events: none
}

[data-grid-style*='grey'] .grid-product:after {
    background-color: var(--color-text-body);
    opacity: .027
}

[data-grid-style*='grey'] .grid-product.grid-product-image-breathing-room--false {
    padding-left: calc(var(--product-grid-margin) / 2);
    padding-right: calc(var(--product-grid-margin) / 2)
}

[data-grid-style*='white'] .grid-item__content {
    background-color: #fff
}

[data-grid-style*='white'] .grid-product:after {
    background-color: #fff;
    z-index: -1
}

[data-grid-style*='white'] .grid-product.grid-product-image-breathing-room--false {
    padding-left: calc(var(--product-grid-margin) / 2);
    padding-right: calc(var(--product-grid-margin) / 2)
}

[data-grid-style='grey-round'] .grid-product:after {
    border-radius: var(--product-radius)
}

[data-grid-style='white-round'] .product-grid-item {
    border-radius: var(--product-radius)
}

[data-grid-style='white-round'] .grid-item__content {
    border-radius: var(--product-radius)
}

[data-grid-style='white-round'] .grid-product:after {
    border-radius: var(--product-radius)
}

.grid-product__image-wrap {
    position: relative;
    margin: var(--product-tile-margin) var(--product-tile-margin) 0
}

[data-view='list'] .grid-product__image-wrap {
    margin-bottom: var(--product-tile-margin)
}

.grid-item__meta-secondary {
    margin-top: 5px;
    flex: 1 0 auto
}

[dir='ltr'] [data-grid-style='simple'] .new-grid:not([data-view='list']) .grid-item__meta {
    margin-left: 0
}

[dir='rtl'] [data-grid-style='simple'] .new-grid:not([data-view='list']) .grid-item__meta {
    margin-right: 0
}

[data-view='list'] .grid-item__link {
    display: flex;
    flex-wrap: nowrap;
    align-items: center
}

[data-view='list'] .grid-product__image-wrap {
    flex: 1 1 45%;
    max-width: 200px;
    margin: calc(var(--product-tile-margin) / 4);
    margin-right: 0
}

[data-view='list'] .grid-item__meta {
    flex: 1 1 55%;
    padding-right: 20px
}

@media only screen and (min-width:769px) {
    [data-view='list'] .grid-item__meta {
        display: flex;
        justify-content: space-between;
        padding-right: 0
    }

    [data-view='list'] .grid-item__meta-secondary {
        margin: 0 20px 0 0;
        text-align: right
    }

    [data-view='list'] .grid-product__title {
        font-size: calc(var(--type-base-size) + 3px)
    }
}

[data-view='list'] .grid-product__price--current {
    font-size: calc(var(--type-base-size) + 6px)
}

[data-view='list'] .grid-item__meta-secondary {
    margin-right: 40px
}

.grid-product__title {
    word-break: break-word;
    -webkit-hyphens: auto;
    hyphens: auto
}

.grid-product__image {
    display: block;
    margin: 0 auto;
    width: 100%;
    height: auto
}

.grid-product__vendor {
    margin-bottom: 5px;
    margin-top: 5px;
    opacity: .65;
    font-size: calc(var(--type-base-size) - 3px)
}

.grid-product__vendor:empty {
    margin: 0
}

.grid-product__price {
    color: var(--color-price)
}

.grid-product__price--current {
    display: inline-block;
    font-weight: var(--type-header-weight);
    margin-right: 5px;
    font-size: calc(var(--type-base-size) + 2px)
}

.grid-product__price--original {
    display: inline-block;
    font-weight: var(--type-header-weight);
    -webkit-text-decoration: line-through;
    text-decoration: line-through;
    font-size: .9em;
    margin-right: 8px
}

.grid-product__price--savings {
    display: inline-block;
    color: var(--color-text-savings);
    white-space: nowrap;
    font-size: .9em
}

[data-view='list'] .grid-product__price--current,
[data-view='list'] .grid-product__price--original,
[data-view='list'] .grid-product__price--savings {
    display: block;
    margin-right: 0
}

[data-view='list'] .grid-product__price--from span:not(.money) {
    display: block
}

.grid-product__tags {
    position: absolute;
    bottom: 0;
    left: 0;
    margin-left: calc(var(--product-tile-margin) * -1);
    z-index: 1
}

.grid-product__add-to-cart {
    margin: 0 16px 16px;
    flex: 0 0 auto
}

.grid-product__tag {
    float: left;
    clear: left;
    line-height: 1;
    padding: 5px 7px;
    margin-top: 5px;
    border-radius: 0 2px 2px 0;
    background-color: var(--color-button-primary);
    color: var(--color-button-primary-text);
    font-size: 10px;
    text-transform: uppercase;
    font-weight: var(--type-header-weight);
    letter-spacing: .05em;
    z-index: 1;
    transition: opacity 0.4s ease
}

.grid-product__tag.grid-product__tag--sold-out {
    background-color: var(--color-body);
    color: var(--color-text-body)
}

@media only screen and (min-width:769px) {
    .grid-product__tag {
        padding: 6px 8px;
        font-size: 11px
    }
}

.grid-product__tag--sale {
    background-color: var(--color-sale-tag);
    color: var(--color-sale-tag-text)
}

@media only screen and (min-width:769px) {
    .grid-product__price--current {
        font-size: calc(var(--type-base-size) + 2px)
    }
}

.quick-add-modal .modal__centered-content {
    min-width: 70vw;
    min-height: 350px;
    max-width: 100%
}

@media only screen and (min-width:769px) {
    .quick-add-modal .modal__centered-content {
        min-width: 500px
    }
}

.quick-add-modal .page-width {
    padding: 0;
    max-width: none
}

.quick-add-modal .product-grid__container>.grid__item {
    display: none
}

.quick-add-modal .product-grid__container .product-grid__content {
    display: block;
    width: 500px;
    max-width: 100%
}

.quick-add-modal .product-single__meta {
    padding: 0
}

.quick-add-modal .collapsibles-wrapper,
.quick-add-modal .social-sharing {
    display: none
}

.grid-product__secondary-image {
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    opacity: 0;
    background-color: var(--color-body);
    transition: opacity 0s cubic-bezier(.26, .54, .32, 1);
    pointer-events: none
}

[data-grid-style*='gridlines'] .grid-product__secondary-image {
    top: 0;
    left: 0;
    right: 0
}

.grid-product__secondary-image img {
    height: 100%;
    width: 100%;
    object-fit: cover
}

.grid-product:hover .grid-product__secondary-image {
    transition-duration: 0.2s;
    opacity: 1
}

.grid-product__color-image {
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    opacity: 0;
    background-color: var(--color-body)
}

.grid-product__color-image.is-active {
    opacity: 1
}

.grid-product__price--from span:not(.money) {
    font-size: calc(var(--type-base-size) - 5px)
}

@media only screen and (min-width:769px) {
    .grid-product__price--from span:not(.money) {
        font-size: calc(var(--type-base-size) - 3px)
    }
}

.grid-product__actions {
    position: absolute;
    top: calc(var(--product-grid-margin) * -1);
    right: calc(var(--product-grid-margin) * -1);
    z-index: 1
}

.grid-product__actions>button,
.grid-product__actions .quick-add-btn,
.grid-product__actions .quick-product__btn {
    display: block;
    margin-bottom: 5px
}

@media only screen and (max-width:768px) {
    [data-grid-style*='gridlines'] .grid-product__actions {
        top: 0;
        right: 0
    }
}

@media only screen and (max-width:768px) {
    [data-grid-style*='gridlines'] .quick-add-btn .btn {
        padding: 6px;
        margin: 0 0 12px 12px;
        border-radius: 0 0 0 10px
    }

    .quick-add-btn .btn--circle {
        line-height: 0
    }

    .quick-add-btn .icon {
        width: 12px;
        height: 12px
    }
}

@media only screen and (min-width:769px) {
    .grid-product__actions {
        opacity: 0;
        transform: translateY(5px);
        transition: opacity 0.25s ease, transform 0.25s ease-out
    }

    .grid-product:hover .grid-product__actions {
        opacity: 1;
        transform: translateY(0)
    }

    .grid-product__actions>button,
    .grid-product__actions .quick-add-btn,
    .grid-product__actions .quick-product__btn {
        transition: transform 0.1s ease-out
    }

    .grid-product__actions>button:hover,
    .grid-product__actions .quick-add-btn:hover,
    .grid-product__actions .quick-product__btn:hover {
        transform: scale(1.15)
    }

    .grid-product:focus-within .grid-product__actions {
        opacity: 1;
        transform: translateY(0);
        transition: opacity 0.25s ease, transform 0.25s ease-out
    }
}

@container product-grid-item (max-width:235px) {
    .grid-product__add-to-cart:not([data-available='true'][data-variants-size='1']) {
        display: none
    }

    .at-add-to-cart__button--add.btn {
        display: none
    }

    .at-add-to-cart__content {
        display: flex
    }
}

.product__photos--beside {
    display: flex;
    width: 100%
}

.product__photos {
    direction: ltr;
    margin-top: 10px
}

.product__photos a {
    display: block;
    max-width: 100%
}

.product__photos img {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    width: 100%;
    opacity: 0
}

@media only screen and (max-width:768px) {
    .page-content--full .product__photos {
        margin-top: 0
    }
}

.product__main-photos {
    position: relative;
    flex: 1 1 auto
}

.product__main-photos img {
    display: none
}

.product__main-photos .flickity-page-dots {
    display: none
}

.product__main-photos .product-slideshow {
    border: 1px solid;
    border-color: var(--color-border)
}

@media only screen and (max-width:768px) {
    .product__main-photos .product-slideshow {
        border: 0
    }
}

@media only screen and (max-width:768px) {
    .product__main-photos {
        margin-bottom: 30px;
        margin-left: calc(var(--page-width-gutter-small) * -1);
        margin-right: calc(var(--page-width-gutter-small) * -1);
        border: 0
    }

    [data-has-slideshow='false'] .product__main-photos {
        margin-bottom: 0
    }

    .product__main-photos .flickity-page-dots {
        display: block
    }
}

.product__thumbs {
    position: relative
}

.product__thumbs--below {
    margin-top: calc(var(--grid-gutter-small) / 2)
}

@media only screen and (min-width:769px) {
    .product__thumbs--below {
        margin-top: calc(var(--gutter) / 2)
    }
}

.product__thumbs--beside {
    flex: 0 0 60px;
    max-width: 60px;
    margin-left: calc(var(--grid-gutter-small) / 2)
}

@media only screen and (min-width:769px) {
    .product__thumbs--beside {
        flex: 0 0 80px;
        max-width: 80px;
        margin-left: calc(var(--gutter) / 2)
    }

    .product__thumbs--beside.product__thumbs-placement--left {
        order: -1;
        margin-left: 0;
        margin-right: calc(var(--gutter) / 2)
    }
}

.product__thumbs--scroller {
    scrollbar-width: none;
    scroll-behavior: smooth;
    -ms-overflow-style: -ms-autohiding-scrollbar
}

.product__thumbs--scroller::-webkit-scrollbar {
    height: 0;
    width: 0
}

.product__thumbs--below .product__thumbs--scroller {
    overflow-x: scroll;
    white-space: nowrap;
    text-align: center
}

.product__thumbs--beside .product__thumbs--scroller {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    overflow-y: scroll
}

.product__thumb-item {
    border: 2px solid #fff0
}

.product__thumb-item a:focus,
.product__thumb-item a.is-active {
    outline: none
}

.product__thumb-item a:focus:before,
.product__thumb-item a.is-active:before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: inset 0 0 0 2px var(--color-text-body);
    z-index: 1
}

.product__thumb-item a:active:before {
    content: none
}

.product__thumbs--beside .product__thumb-item {
    margin-bottom: calc(var(--grid-gutter-small) / 2)
}

@media only screen and (min-width:769px) {
    .product__thumbs--beside .product__thumb-item {
        margin-bottom: calc(var(--gutter) / 2)
    }
}

.product__thumbs--beside .product__thumb-item:last-child {
    margin-bottom: 0
}

.product__thumbs--below .product__thumb-item {
    display: inline-block;
    vertical-align: middle;
    margin-right: calc(var(--grid-gutter-small) / 2);
    max-width: 80px
}

@media only screen and (min-width:769px) {
    .product__thumbs--below .product__thumb-item {
        margin-right: calc(var(--gutter) / 2)
    }
}

.product__thumbs--below .product__thumb-item:last-child {
    margin-right: 0
}

.product__thumb .image-wrap img {
    border: 1px solid;
    border-color: var(--color-border);
    position: static
}

.product-slideshow.flickity-enabled .product-main-slide {
    display: none
}

.product-slideshow.flickity-enabled .flickity-viewport .product-main-slide {
    display: block
}

.product__thumb-arrow {
    position: absolute;
    background: var(--color-body);
    color: var(--color-text-body);
    transform: none;
    border-radius: 0;
    padding: 0;
    z-index: 2
}

.product__thumb-arrow .icon {
    display: inline-block;
    width: 6px;
    height: 10px
}

.product__thumbs[data-position='below'] .product__thumb-arrow {
    top: 0;
    height: 100%;
    width: 25px
}

.product__thumbs[data-position='below'] .product__thumb-arrow.product__thumb-arrow--prev {
    left: 0;
    text-align: left
}

.product__thumbs[data-position='below'] .product__thumb-arrow.product__thumb-arrow--next {
    right: 0;
    text-align: right
}

.product__thumbs[data-position='beside'] .product__thumb-arrow {
    width: 100%
}

.product__thumbs[data-position='beside'] .product__thumb-arrow .icon {
    margin: 0 auto;
    transform: rotate(90deg)
}

.product__thumbs[data-position='beside'] .product__thumb-arrow.product__thumb-arrow--prev {
    top: 0;
    left: auto;
    padding-bottom: 10px
}

.product__thumbs[data-position='beside'] .product__thumb-arrow.product__thumb-arrow--next {
    top: auto;
    bottom: 0;
    right: auto;
    padding-top: 10px
}

.product__thumb {
    position: relative;
    display: block;
    cursor: pointer
}

.product__thumb-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: var(--color-text-body);
    border-radius: 100px;
    padding: 6px;
    z-index: 1;
    opacity: 1;
    font-size: 0
}

.product__thumb-icon .icon {
    fill: var(--color-body);
    width: 10px;
    height: 10px
}

@media only screen and (min-width:769px) {
    .product__thumb-icon .icon {
        width: 13px;
        height: 13px
    }
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area {
    opacity: 1;
    background: var(--color-body);
    border-color: var(--color-text-body-alpha-005);
    border-radius: 50px
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button {
    color: var(--color-text-body)
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:hover {
    color: var(--color-text-body)
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:active,
.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control.focus-visible:focus {
    color: var(--color-text-body);
    background-color: var(--color-text-body-alpha-005)
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:not(:last-child):after {
    border-color: var(--color-text-body-alpha-005)
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster {
    background-color: var(--color-text-body);
    color: var(--color-body);
    border-radius: 100%;
    border: 1px solid;
    border-color: var(--color-body-alpha-005)
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover,
.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus {
    color: var(--color-body)
}

.product-single__view-in-space {
    display: block;
    color: var(--color-text-body);
    background-color: var(--color-text-body-alpha-008);
    width: 80%;
    width: calc(80% - 4px);
    margin: 40px 10% 10px;
    padding: 5px 10px 10px
}

.product-single__view-in-space[data-shopify-xr-hidden] {
    display: none
}

.product-single__view-in-space-text {
    font-size: calc(var(--type-base-size) * 0.85);
    display: inline-block;
    vertical-align: middle;
    margin-left: 5px
}

.shopify-model-viewer-ui,
.shopify-model-viewer-ui model-viewer {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.shopify-model-viewer-ui__button[hidden] {
    display: none
}

@keyframes inventory-pulse {
    0% {
        opacity: .5
    }

    100% {
        transform: scale(2.5);
        opacity: 0
    }
}

.icon--inventory:before,
.icon--inventory:after {
    width: 9px;
    height: 9px;
    background: #54c63a;
    border-radius: 9px;
    position: absolute;
    left: 0;
    top: 0;
    content: '';
    margin: 8px
}

.icon--inventory:before {
    animation: inventory-pulse 2s linear infinite
}

.inventory--low .icon--inventory:before,
.inventory--low .icon--inventory:after {
    background: #f4af29
}

product-inventory {
    padding-top: 20px
}

.product-inventory__points {
    list-style: none;
    padding: 0;
    margin: 0
}

.quick-add-modal .product-inventory__points {
    display: none
}

.product-inventory__point {
    display: block;
    margin-bottom: 10px
}

.product-inventory__point:last-child {
    margin-bottom: 0
}

.product-inventory__point .icon {
    position: relative;
    width: 25px;
    height: 25px;
    margin-right: 10px
}

[dir='rtl'] .product-inventory__point .icon {
    margin-right: 0;
    margin-left: 10px
}

.product-block--sales-point+.product-block--inventory-point {
    margin-top: -20px
}

.product-tags {
    --gap: 6px;
    display: flex;
    gap: var(--gap)
}

.product-grid-item .product-tags {
    padding: 0 16px 16px
}

@media only screen and (max-width:959px) {
    .product-grid-item .product-tags {
        padding: 0 15px 15px
    }
}

.product-tag {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    padding: .2rem .4rem;
    margin-right: 2px;
    margin-top: 4px;
    margin-bottom: 4px;
    white-space: nowrap;
    border: 1px solid;
    font-size: 13px;
    transition: ease-in-out 0.3s opacity
}

.product-tag.product-tag--has-link:hover {
    opacity: .7
}

.product-tag .icon {
    display: flex;
    width: 14px;
    height: 14px;
    margin-right: .25rem
}

.product-tag a {
    display: inline-flex;
    align-items: center;
    justify-content: space-between
}

.product-tag--style-round {
    border-radius: 20px
}

.product-tag--none {
    border-color: var(--color-text-body-alpha-008)
}

.product-tag--scheme_1 {
    background-color: var(--color-scheme-1-bg);
    border-color: var(--color-scheme-1-bg);
    color: var(--color-scheme-1-text)
}

.product-tag--scheme_1 a {
    color: var(--color-scheme-1-text)
}

.product-tag--scheme_2 {
    background-color: var(--color-scheme-2-bg);
    border-color: var(--color-scheme-2-bg);
    color: var(--color-scheme-2-text)
}

.product-tag--scheme_2 a {
    color: var(--color-scheme-2-text)
}

.product-tag--scheme_3 {
    background-color: var(--color-scheme-3-bg);
    border-color: var(--color-scheme-3-bg);
    color: var(--color-scheme-3-text)
}

.product-tag--scheme_3 a {
    color: var(--color-scheme-3-text)
}

.js-qty__wrapper {
    display: inline-block;
    position: relative;
    max-width: 100px;
    min-width: 60px;
    overflow: visible;
    background-color: var(--color-body);
    color: var(--color-text-body)
}

.js-qty__wrapper.is-loading {
    opacity: .5;
    pointer-events: none
}

.js-qty__num {
    display: block;
    background: none;
    text-align: center;
    width: 100%;
    padding: 5px 20px;
    margin: 0
}

.js-qty__adjust {
    cursor: pointer;
    position: absolute;
    display: block;
    top: 0;
    bottom: 0;
    border: 0 none;
    background: none;
    text-align: center;
    overflow: hidden;
    padding: 0 10px;
    line-height: 1;
    -webkit-user-select: none;
    user-select: none;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transition: background-color 0.1s ease-out;
    z-index: 1;
    fill: var(--color-text-body)
}

.js-qty__adjust .icon {
    display: block;
    font-size: 8px;
    vertical-align: middle;
    width: 10px;
    height: 10px;
    fill: inherit
}

.js-qty__adjust:hover {
    background-color: var(--color-body-dim);
    color: var(--color-text-body)
}

.js-qty__num:active~.js-qty__adjust,
.js-qty__num:focus~.js-qty__adjust {
    border-color: var(--color-text-body)
}

.js-qty__adjust--plus {
    right: 0
}

.js-qty__adjust--minus {
    left: 0
}

advanced-accordion {
    display: block;
    border-bottom: 1px solid var(--color-border)
}

advanced-accordion[data-disabled='true'] .accordion__title {
    cursor: default
}

.advanced-accordion--1-per-row .accordion__content-block {
    width: 100%
}

.advanced-accordion--2-per-row .accordion__content-block {
    width: 50%
}

.advanced-accordion--3-per-row .accordion__content-block {
    width: 33.333%
}

.advanced-accordion--4-per-row .accordion__content-block {
    width: 25%
}

.advanced-accordion--5-per-row .accordion__content-block {
    width: 20%
}

.accordion__content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap
}

.accordion__title {
    width: 100%;
    background: #fff0;
    border: 0;
    padding: 20px 0;
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer
}

.accordion__title h2 {
    margin: 0
}

.accordion__title svg.icon {
    --icon-size: 24px;
    stroke-width: calc(var(--icon-stroke-width) / 1.125);
    transition: ease-in-out 0.3s transform
}

details[open] .accordion__title svg {
    transform: rotate(180deg)
}

.accordion__content-block {
    padding: 20px
}

@media only screen and (max-width:768px) {
    .accordion__content-block {
        flex: 100% 0 0 !important;
        padding: 0 15px 20px 15px
    }
}

@media only screen and (max-width:768px) {
    .accordion__content-block.two-per-row-mobile {
        flex: 50% 0 0 !important
    }
}

.accordion__content-block .content-block__image {
    margin-bottom: 20px
}

.accordion__content-block img {
    height: auto
}

a.accordion-link-block__link {
    display: flex;
    justify-content: space-between
}

a.accordion-link-block__link svg {
    height: 1rem;
    margin-left: 10px
}

.custom-content {
    display: flex;
    align-items: stretch;
    flex-wrap: wrap;
    width: auto;
    margin-bottom: calc(var(--grid-gutter) * -1);
    margin-left: calc(var(--grid-gutter) * -1)
}

@media only screen and (max-width:768px) {
    .custom-content {
        margin-bottom: calc(var(--grid-gutter-small) * -1);
        margin-left: calc(var(--grid-gutter-small) * -1)
    }
}

.custom__item {
    flex: 0 0 auto;
    margin-bottom: var(--grid-gutter);
    padding-left: var(--grid-gutter);
    max-width: 100%
}

@media only screen and (max-width:768px) {
    .custom__item {
        flex: 0 0 auto;
        padding-left: var(--grid-gutter-small);
        margin-bottom: var(--grid-gutter-small)
    }

    .custom__item.small--one-half {
        flex: 1 0 50%;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto
    }
}

.custom__item img {
    display: block
}

.custom__item-inner {
    position: relative;
    display: inline-block;
    text-align: left;
    max-width: 100%;
    width: 100%
}

.custom__item-inner--video,
.custom__item-inner--html {
    display: block
}

.custom__item-inner--image {
    width: 100%
}

.custom__item-inner--html img {
    display: block;
    margin: 0 auto
}

.custom__item-inner--placeholder-image {
    width: 100%
}

.align--top-middle {
    text-align: center
}

.align--top-right {
    text-align: right
}

.align--middle-left {
    align-self: center
}

.align--center {
    align-self: center;
    text-align: center
}

.align--middle-right {
    align-self: center;
    text-align: right
}

.align--bottom-left {
    align-self: flex-end
}

.align--bottom-middle {
    align-self: flex-end;
    text-align: center
}

.align--bottom-right {
    align-self: flex-end;
    text-align: right
}

.age-verification-popup .rte {
    margin-top: 20px;
    margin-bottom: 20px
}

@media only screen and (min-width:769px) {
    .age-verification-popup .rte {
        margin-bottom: 30px
    }
}

.age-verification-popup .age-verification-popup__btns-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px
}

.age-verification-popup__background-image-wrapper {
    overflow: hidden;
    width: 100%;
    height: 100%;
    position: absolute
}

.age-verification-popup__background-image {
    object-fit: cover;
    width: 100%;
    height: 100%
}

.age-verification-popup__content--active {
    opacity: 1;
    transition: opacity 1.5s ease-in
}

.age-verification-popup__content--inactive,
.age-verification-popup__content--inactive *,
.age-verification-popup__content--inactive .btn {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
    visibility: hidden;
    padding: 0;
    border: 0;
    margin: 0;
    line-height: 0;
    font-size: 0
}

.age-verification-popup__decline-content--inactive,
.age-verification-popup__decline-content--inactive *,
.age-verification-popup__decline-content--inactive .btn {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
    visibility: hidden;
    padding: 0;
    border: 0;
    margin: 0;
    line-height: 0;
    font-size: 0
}

.age-verification-popup__decline-content--active {
    opacity: 1;
    transition: opacity 1.5s ease-in
}

.age-verification-popup__content-wrapper {
    text-align: center;
    max-width: 520px;
    margin: 0 auto
}

.age-verification-popup {
    top: 0;
    bottom: 0
}

.age-verification-popup.age-verification-popup--image-false {
    position: fixed !important;
    z-index: 30 !important
}

.age-verification-popup.age-verification-popup--image-false.modal:before {
    background-color: #fff0;
    animation: none
}

.age-verification-popup.age-verification-popup--image-false .modal__inner {
    box-shadow: none;
    background-color: #fff0
}

.age-verification-popup.age-verification-popup--image-false.color-scheme-none {
    background-color: var(--color-body)
}

.age-verification-popup.color-scheme-none .btn.color-scheme-reversed {
    color: var(--color-button-primary);
    background-color: var(--color-button-primary-text);
    border-color: var(--color-button-primary)
}

.article-tag__wrapper {
    margin-bottom: 20px
}

.article-tag__wrapper .label {
    margin-right: 10px
}

.article__comment {
    margin-bottom: 20px
}

.article__comment:last-child {
    margin-bottom: 0
}

.comment-author {
    margin-bottom: 0
}

.comment-date {
    font-size: calc(var(--type-base-size) * 0.85);
    display: block;
    margin-top: 3px
}

@media only screen and (max-width:768px) {
    .comment-date {
        margin-bottom: calc(var(--gutter) / 2)
    }
}

.background-media-text {
    position: relative;
    width: 100%;
    overflow: hidden;
    background: var(--color-large-image-bg)
}

.background-media-text__inner {
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%
}

.background-media-text__aligner {
    margin: calc(var(--gutter) * 2)
}

.background-media-text__text {
    text-align: left;
    font-size: 1.1em;
    background: var(--color-body);
    padding: var(--gutter);
    width: 380px;
    border-radius: var(--roundness);
    overflow: hidden
}

html[dir='rtl'] .background-media-text__text {
    text-align: right
}

@media only screen and (max-width:768px) {
    .background-media-text__text {
        text-align: center
    }
}

.background-media-text__text .btn {
    margin-top: calc(var(--gutter) / 2)
}

@media only screen and (min-width:769px) {
    .background-media-text--right>div {
        float: right
    }
}

.background-media-text__container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

@media only screen and (max-width:768px) {
    .background-media-text__container {
        position: relative;
        height: 240px
    }

    .background-media-text__inner {
        position: relative;
        bottom: 0
    }

    .background-media-text__aligner {
        margin: -10px 10px 10px
    }

    .background-media-text__text {
        padding: calc(var(--gutter) * 0.75);
        width: auto
    }

    .background-media-text.loading:before,
    .background-media-text.loading:after {
        top: 117px
    }
}

@media only screen and (min-width:769px) {
    .background-media-text--450 {
        min-height: 450px
    }

    .background-media-text--550 {
        min-height: 550px
    }

    .background-media-text--650 {
        min-height: 650px
    }

    .background-media-text--750 {
        min-height: 750px
    }

    .background-media-text__image {
        height: 100%;
        object-fit: cover
    }

    .background-media-text__image,
    .background-media-text__image svg {
        opacity: 0
    }
}

.background-media-text__image {
    opacity: 0;
    animation: none;
    transition: none
}

.loaded .background-media-text__image,
.loaded .background-media-text__image svg {
    animation: zoom-fade 1s cubic-bezier(.26, .54, .32, 1) 0s forwards;
    transition: none
}

@media only screen and (min-width:769px) {
    .loaded .background-media-text__inner .animation-contents {
        transform: translateY(0);
        opacity: 1;
        transition: all 0.8s cubic-bezier(.26, .54, .32, 1) 0.5s
    }
}

.blog-layout {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column
}

.blog-layout__main {
    margin-bottom: 20px
}

.blog-layout__sidebar {
    order: 1
}

.blog-layout>div:not(.blog-layout__main):not(.blog-layout__sidebar) {
    flex: 1 0 100%
}

@media only screen and (min-width:769px) {
    .blog-layout {
        flex-direction: row;
        flex-wrap: wrap
    }

    .blog-layout__main {
        flex: 1 1 calc(60% - 90px)
    }

    .blog-layout__sidebar {
        order: 0;
        flex: 0 0 calc(40% - 90px);
        align-self: flex-start;
        padding-left: 0;
        margin-left: 0;
        border-left: 0;
        padding-right: 45px;
        margin-right: 45px;
        border-right: 1px solid;
        border-right-color: var(--color-border)
    }

    [dir='rtl'] .blog-layout__sidebar {
        padding-right: 0;
        margin-right: 0;
        border-right: 0;
        padding-left: 45px;
        margin-left: 45px;
        border-left: 1px solid;
        border-left-color: var(--color-border)
    }

    .blog-layout__main+.blog-layout__sidebar {
        padding-right: 0;
        margin-right: 0;
        border-right: 0;
        padding-left: 45px;
        margin-left: 45px;
        border-left: 1px solid;
        border-left-color: var(--color-border)
    }

    [dir='rtl'] .blog-layout__main+.blog-layout__sidebar {
        padding-left: 0;
        margin-left: 0;
        border-left: 0;
        padding-right: 45px;
        margin-right: 45px;
        border-right: 1px solid;
        border-right-color: var(--color-border)
    }
}

.article-tag {
    display: inline-block;
    background-color: var(--color-body);
    color: var(--color-text-body);
    border: 1px solid;
    border-color: var(--color-text-body);
    padding: 3px 9px;
    margin: 5px;
    font-size: 12px;
    font-weight: var(--type-header-weight);
    letter-spacing: .15em;
    text-transform: uppercase
}

.tag--inline .article-tag {
    margin: 0
}

.article-tag:hover,
.article-tag:active {
    background-color: var(--color-body);
    color: var(--color-text-body)
}

@media only screen and (max-width:768px) {
    .article-tag {
        padding: 4px 6px;
        font-size: 10px;
        margin: 3px
    }
}

.tag--remove,
.tag--inline {
    display: inline-block;
    position: relative;
    margin: 0 10px 13px 0
}

.tag--remove a,
.tag--inline a {
    display: block;
    text-align: left;
    padding: 7px 15px 7px 36px;
    min-width: 0
}

.tag--remove .icon,
.tag--inline .icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--color-button-primary-text)
}

.tag--inline a {
    padding: 7px 15px 7px 15px
}

.blog-layout {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column
}

.blog-layout__main {
    margin-bottom: 20px
}

.blog-layout>div:not(.blog-layout__main):not(.blog-layout__sidebar) {
    flex: 1 0 100%
}

@media only screen and (min-width:769px) {
    .blog-layout {
        flex-direction: row;
        flex-wrap: wrap
    }

    .blog-layout__main {
        flex: 1 1 calc(60% - 90px)
    }

    .blog-layout__main+.blog-layout__sidebar {
        padding-right: 0;
        margin-right: 0;
        border-right: 0;
        padding-left: 45px;
        margin-left: 45px;
        border-left: 1px solid;
        border-left-color: var(--color-border)
    }

    [dir='rtl'] .blog-layout__main+.blog-layout__sidebar {
        padding-left: 0;
        margin-left: 0;
        border-left: 0;
        padding-right: 45px;
        margin-right: 45px;
        border-right: 1px solid;
        border-right-color: var(--color-border)
    }
}

.collection-hero {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    background-color: var(--color-large-image-bg)
}

@media only screen and (min-width:769px) {
    .collection-hero {
        height: 400px
    }
}

.collection-hero__content {
    --z-index-overlay: -1;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    display: flex;
    align-items: flex-end;
    padding: calc(var(--gutter) / 2) 0;
    color: #fff;
    z-index: 1
}

@media only screen and (min-width:769px) {
    .collection-hero__content {
        padding: var(--gutter) 0
    }
}

.collection-hero__content a {
    color: #fff
}

.overlaid-header .collection-hero__content {
    padding-top: 70px
}

@media only screen and (min-width:769px) {
    .overlaid-header .collection-hero__content {
        padding-top: 100px
    }
}

.collection-hero__content .page-width {
    width: 100%
}

.collection-hero__content:after {
    background: linear-gradient(45deg, rgb(0 0 0 / .3), transparent 50%);
    pointer-events: none
}

.collection-hero__image {
    transform: scale(1);
    transition: transform 1s cubic-bezier(.18, .63, .25, 1), opacity 0.7s ease;
    animation: none;
    object-fit: cover
}

.return-section {
    margin-top: var(--index-section-padding);
    padding: var(--index-section-padding) 0
}

.return-link {
    text-align: center;
    padding: 15px 25px
}

.return-link .icon {
    width: 20px;
    margin-right: 8px
}

.note,
.errors {
    border-radius: var(--input-radius);
    padding: 6px 12px;
    margin-bottom: calc(var(--gutter) / 2);
    border: 1px solid #fff0;
    text-align: left
}

.note ul,
.errors ul,
.note ol,
.errors ol {
    margin-top: 0;
    margin-bottom: 0
}

.note li:last-child,
.errors li:last-child {
    margin-bottom: 0
}

.note p,
.errors p {
    margin-bottom: 0
}

.note {
    border-color: var(--color-border)
}

.errors ul {
    list-style: disc outside;
    margin-left: 20px
}

.note--success {
    color: var(--success-green);
    background-color: var(--success-green-bg);
    border-color: var(--success-green)
}

.note--success a {
    color: var(--success-green);
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.note--success a:hover {
    -webkit-text-decoration: none;
    text-decoration: none
}

.form-error,
.errors {
    color: var(--error-red);
    background-color: var(--error-red-bg);
    border-color: var(--error-red)
}

.form-error a,
.errors a {
    color: var(--error-red);
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.form-error a:hover,
.errors a:hover {
    -webkit-text-decoration: none;
    text-decoration: none
}

.contact-form--content-position-left,
.contact-form--content-position-right {
    --index-section-padding: 0
}

.contact-form--content-position-left .grid__item,
.contact-form--content-position-right .grid__item {
    width: 100%
}

.contact-form--content-position-left .page-width,
.contact-form--content-position-right .page-width {
    max-width: none;
    padding: 0;
    display: grid
}

@media only screen and (min-width:769px) {

    .contact-form--content-position-left .page-width,
    .contact-form--content-position-right .page-width {
        grid-template-columns: 1fr 1fr
    }
}

.contact-form--content-position-left [data-image-type],
.contact-form--content-position-right [data-image-type] {
    position: relative;
    grid-area: image
}

.contact-form--content-position-left [data-image-type]:after,
.contact-form--content-position-right [data-image-type]:after {
    content: '';
    position: absolute;
    top: 0;
    height: 100%;
    width: 100%
}

.contact-form--content-position-left .section-header,
.contact-form--content-position-right .section-header {
    padding: 40px 32px 16px;
    margin: 0;
    grid-area: heading
}

@media only screen and (min-width:769px) {

    .contact-form--content-position-left .section-header,
    .contact-form--content-position-right .section-header {
        padding: 80px 80px 24px
    }
}

.contact-form--content-position-left .form-vertical,
.contact-form--content-position-right .form-vertical {
    padding: 0 32px 24px;
    margin: 0;
    grid-area: form
}

@media only screen and (min-width:769px) {

    .contact-form--content-position-left .form-vertical,
    .contact-form--content-position-right .form-vertical {
        padding: 0 80px 80px
    }
}

.contact-form--content-position-left[class*='color-scheme-']:not(.color-scheme-none) input,
.contact-form--content-position-right[class*='color-scheme-']:not(.color-scheme-none) input,
.contact-form--content-position-left[class*='color-scheme-']:not(.color-scheme-none) textarea,
.contact-form--content-position-right[class*='color-scheme-']:not(.color-scheme-none) textarea {
    background: #fff0
}

.contact-form--content-position-left .form-vertical .btn[type='submit'],
.contact-form--content-position-right .form-vertical .btn[type='submit'] {
    width: 100%
}

.contact-form--content-position-left.contact-form--overlay.color-scheme-none [data-image-type]:after,
.contact-form--content-position-right.contact-form--overlay.color-scheme-none [data-image-type]:after {
    background: linear-gradient(0deg, #fff0 25%, var(--color-scheme-1-bg) 90%)
}

.contact-form--content-position-left.contact-form--overlay.color-scheme-1 [data-image-type]:after,
.contact-form--content-position-right.contact-form--overlay.color-scheme-1 [data-image-type]:after {
    background: linear-gradient(0deg, #fff0 25%, var(--color-scheme-1-bg) 90%)
}

.contact-form--content-position-left.contact-form--overlay.color-scheme-2 [data-image-type]:after,
.contact-form--content-position-right.contact-form--overlay.color-scheme-2 [data-image-type]:after {
    background: linear-gradient(0deg, #fff0 25%, var(--color-scheme-2-bg) 90%)
}

.contact-form--content-position-left.contact-form--overlay.color-scheme-3 [data-image-type]:after,
.contact-form--content-position-right.contact-form--overlay.color-scheme-3 [data-image-type]:after {
    background: linear-gradient(0deg, #fff0 25%, var(--color-scheme-3-bg) 90%)
}

.contact-form--content-position-left .page-width {
    grid-template-areas: 'image' 'heading' 'form'
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-left .page-width {
        grid-template-areas: 'heading image' 'form image'
    }
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-left.contact-form--overlay [data-image-type]:after {
        left: 0
    }
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-left.contact-form--overlay.color-scheme-none [data-image-type]:after {
        background: linear-gradient(270deg, #fff0 25%, var(--color-body) 90%)
    }
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-left.contact-form--overlay.color-scheme-1 [data-image-type]:after {
        background: linear-gradient(270deg, #fff0 25%, var(--color-scheme-1-bg) 90%)
    }
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-left.contact-form--overlay.color-scheme-2 [data-image-type]:after {
        background: linear-gradient(270deg, #fff0 25%, var(--color-scheme-2-bg) 90%)
    }
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-left.contact-form--overlay.color-scheme-3 [data-image-type]:after {
        background: linear-gradient(270deg, #fff0 25%, var(--color-scheme-2-bg) 90%)
    }
}

.contact-form--content-position-right .page-width {
    grid-template-areas: 'heading' 'form' 'image';
    background: inherit
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-right .page-width {
        grid-template-areas: 'image heading' 'image form'
    }
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-right.contact-form--overlay [data-image-type]:after {
        right: 0
    }
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-right.contact-form--overlay.color-scheme-none [data-image-type]:after {
        background: linear-gradient(90deg, #fff0 25%, var(--color-body) 90%)
    }
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-right.contact-form--overlay.color-scheme-1 [data-image-type]:after {
        background: linear-gradient(90deg, #fff0 25%, var(--color-scheme-1-bg) 90%)
    }
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-right.contact-form--overlay.color-scheme-2 [data-image-type]:after {
        background: linear-gradient(90deg, #fff0 25%, var(--color-scheme-2-bg) 90%)
    }
}

@media only screen and (min-width:769px) {
    .contact-form--content-position-right.contact-form--overlay.color-scheme-3 [data-image-type]:after {
        background: linear-gradient(90deg, #fff0 25%, var(--color-scheme-3-bg) 90%)
    }
}

.contact-form__image {
    opacity: 1
}

@media only screen and (min-width:769px) {
    .contact-form__image {
        height: 100%;
        width: 100%;
        object-fit: cover
    }
}

.countdown-wrapper {
    position: relative
}

.countdown__background-image-wrapper {
    width: 100%;
    height: 100%;
    position: absolute
}

.countdown__background-image {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    top: 0;
    left: 0
}

.countdown__mobile-image-wrapper {
    width: 100%;
    height: 100%;
    position: absolute
}

.countdown__mobile-image {
    width: 100%;
    height: 100%;
    object-fit: cover
}

.countdown-layout--banner .countdown__content {
    padding: 2rem
}

@media only screen and (min-width:769px) {
    .countdown-layout--banner .countdown__content {
        display: flex;
        flex-wrap: nowrap;
        flex-direction: row;
        justify-content: space-around;
        gap: 1rem;
        padding: 1.6rem 2rem
    }
}

.countdown-layout--banner.countdown-blocks--2.page-width .countdown__content {
    justify-content: space-around
}

.countdown-layout--banner.countdown-blocks--2 .countdown__content {
    justify-content: center
}

@media only screen and (min-width:769px) {
    .countdown-layout--banner .countdown__block {
        width: 33%
    }
}

.countdown__content {
    width: 100%;
    height: auto;
    max-height: 650px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 5rem 2rem;
    text-align: center
}

.page-width .countdown__content {
    position: relative
}

@media only screen and (min-width:769px) {
    .countdown-layout--hero .countdown__content {
        height: 100vh
    }
}

.countdown__block {
    width: 100%;
    margin: 1rem auto;
    z-index: 3
}

.countdown__block--content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center
}

.countdown__block--content .countdown__block--button {
    width: 100%;
    margin: 0
}

@media only screen and (min-width:769px) {

    .countdown-blocks--2.countdown-layout--banner .countdown__block--timer,
    .countdown-blocks--2.countdown-layout--banner .countdown__block--content {
        width: 50%
    }
}

@media only screen and (min-width:769px) {
    .countdown-blocks--2.countdown-layout--banner .countdown__block--button.button-block-active {
        width: 50%
    }
}

.countdown__text-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center
}

@media only screen and (min-width:769px) {
    .countdown-layout--hero .countdown__text-wrapper {
        width: 50%
    }
}

.countdown__text-wrapper--content-alignment-left {
    text-align: left
}

.countdown__text-wrapper--content-alignment-right {
    text-align: right
}

.countdown__display {
    display: flex;
    justify-content: center
}

.countdown__display--loaded {
    opacity: 1;
    visibility: visible;
    transition: opacity ease-in 0.3s
}

.countdown__display--visible+.countdown__timer-message--visible {
    margin-top: 1.5rem
}

.countdown__display--hidden {
    visibility: hidden;
    opacity: 0;
    width: 0;
    height: 0;
    margin: 0;
    transition: opacity ease-out 3s
}

.countdown__display-block {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    align-content: center;
    border-right: 1px solid;
    padding: 0 1rem
}

.countdown__display-block h2 {
    margin-bottom: 16px
}

@media only screen and (min-width:769px) {
    .countdown__display-block h2 {
        margin-bottom: 4px
    }
}

.countdown__display-block span {
    font-size: .6rem;
    letter-spacing: 1.7px
}

@media only screen and (min-width:769px) {
    .countdown__display-block span {
        font-size: .75rem
    }
}

.countdown__display-block:last-child {
    border-right: none
}

[dir='rtl'] .countdown__display-block:last-child {
    border-right: 1px solid
}

[dir='rtl'] .countdown__display-block:first-child {
    border-right: none
}

.countdown__timer-message {
    opacity: 0;
    visibility: hidden;
    margin: 0;
    height: 0
}

.countdown__timer-message--visible {
    opacity: 1;
    visibility: visible;
    transition: opacity ease-in 1s;
    height: auto
}

.countdown__block--hidden {
    opacity: 0;
    visibility: hidden;
    margin: 0;
    transition: opacity ease-out 1s;
    width: 0 !important
}

.countdown__overlay::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    background-color: var(--countdown-overlay-rgba)
}

.countdown__image--blur-true img {
    filter: blur(4px);
    transform: scale(1.03)
}

.countdown__image--blur-true .countdown__background-image-wrapper {
    overflow: hidden
}

.countdown__image--blur-true .countdown__mobile-image-wrapper {
    overflow: hidden
}

.countdown-wrapper.page-width .countdown__content,
.countdown-wrapper.page-width .countdown__background-image-wrapper,
.countdown-wrapper.page-width .countdown__background-image,
.countdown-wrapper.page-width .countdown__mobile-image-wrapper,
.countdown-wrapper.page-width .countdown__mobile-image,
.countdown-wrapper.page-width .countdown__overlay::after {
    border-radius: var(--roundness)
}

.countdown__block--content .countdown__block--button {
    margin-top: 1.5rem
}

.color-scheme-1 .countdown__button.btn--secondary {
    background-color: #fff0;
    color: var(--color-scheme-1-text);
    border-color: var(--color-scheme-1-text)
}

.color-scheme-2 .countdown__button.btn--secondary {
    background-color: #fff0;
    color: var(--color-scheme-2-text);
    border-color: var(--color-scheme-2-text)
}

.color-scheme-3 .countdown__button.btn--secondary {
    background-color: #fff0;
    color: var(--color-scheme-3-text);
    border-color: var(--color-scheme-3-text)
}

.collapsible-trigger {
    color: inherit;
    position: relative
}

.collapsible-trigger__layout {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.collapsible-trigger__layout--inline {
    position: relative;
    justify-content: flex-start
}

.collapsible-trigger__layout--inline>span {
    padding-right: 15px
}

.collapsible-trigger__layout--inline .collapsible-trigger__icon {
    position: static;
    transform: none
}

.collapsible-trigger__icon {
    display: block;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: var(--collapsible-icon-width);
    height: var(--collapsible-icon-width)
}

[dir='rtl'] .collapsible-trigger__icon {
    left: 0;
    right: auto
}

.collapsible-trigger__icon .icon {
    display: block;
    width: var(--collapsible-icon-width);
    height: var(--collapsible-icon-width);
    transition: all 0.1s ease-in
}

.collapsible-trigger.is-open .collapsible-trigger__icon>.icon-chevron-down {
    transform: rotate(180deg)
}

.collapsible-trigger--inline {
    font-weight: var(--type-header-weight);
    padding: 11px 0 11px 20px
}

.collapsible-trigger--inline .collapsible-trigger__icon {
    right: auto;
    left: 0
}

.collapsible-content {
    transition: opacity 0.2s ease, height 0.15s ease, transform 0.3s cubic-bezier(.25, .46, .45, .94);
    transform: translateY(-10px)
}

.collapsible-content.is-open {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, height 0.25s ease, transform 0.3s cubic-bezier(.25, .46, .45, .94)
}

.collapsible-content--all {
    visibility: hidden;
    overflow: hidden;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    opacity: 0;
    height: 0
}

@media only screen and (min-width:769px) {
    .collapsible-content--all.is-open {
        overflow: initial;
        visibility: visible;
        opacity: 1;
        height: auto
    }
}

.collapsible-content--all.is-open {
    border-bottom: 1px solid;
    border-color: var(--color-border)
}

.filter-wrapper .collapsible-content--all.is-open {
    border: 0
}

@media only screen and (max-width:768px) {
    .collapsible-content--small {
        visibility: hidden;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        opacity: 0;
        height: 0
    }

    .collapsible-content--small .collapsible-content__inner {
        transform: translateY(40px)
    }
}

@media only screen and (min-width:769px) {
    .collapsible-content__inner {
        padding: 0 0 35px
    }
}

.collapsible-trigger[aria-expanded='true'] .collapsible-label__closed {
    display: none
}

.collapsible-label__open {
    display: none
}

.collapsible-trigger[aria-expanded='true'] .collapsible-label__open {
    display: inline-block
}

.index-section--faq {
    margin-bottom: 20px
}

.featured-collection__carousel {
    display: flex
}

@media only screen and (max-width:959px) {
    .featured-collection__carousel {
        flex-direction: column
    }
}

.featured-collection__carousel .grid-product__actions {
    top: var(--product-grid-margin);
    right: 0
}

.featured-collection__carousel .featured-collection__block-wrapper {
    display: flex;
    flex-direction: column;
    flex: 0 0 25%;
    padding-right: 20px
}

@media only screen and (max-width:959px) {
    .featured-collection__carousel .featured-collection__block-wrapper {
        padding-right: var(--page-width-padding)
    }
}

.featured-collection__block-wrapper-content-position--top {
    justify-content: flex-start
}

.featured-collection__block-wrapper-content-position--center {
    justify-content: center
}

.featured-collection__block-wrapper-content-position--bottom {
    justify-content: flex-end
}

.featured-collection__block {
    padding-bottom: 20px
}

.featured-collection__block:last-child {
    padding-bottom: 0
}

@media only screen and (max-width:959px) {
    .featured-collection__block:last-child {
        padding-bottom: 20px
    }
}

.featured-collection__carousel-grid-items {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    width: 100%
}

.featured-collection__carousel .grid-item {
    flex: 1 0 40%;
    margin-bottom: 0
}

@media only screen and (max-width:959px) {
    .featured-collection__carousel .grid-item {
        flex: 1 0 60%
    }
}

@media only screen and (max-width:959px) {
    .svg-mask--disabled-mobile .image-element {
        -webkit-mask-image: none !important;
        mask-image: none !important
    }
}

@media only screen and (max-width:959px) {
    .medium-down--banner-image .image-wrap {
        padding-bottom: 35% !important
    }

    .medium-down--banner-image .image-element {
        height: 100%
    }
}

.page-width--no-right-padding.page-width {
    padding-right: 0
}

.site-footer {
    display: block;
    font-size: calc(var(--type-base-size) - 1px);
    background-color: var(--color-footer);
    color: var(--color-footer-text)
}

.site-footer a {
    color: var(--color-footer-text)
}

.site-footer .faux-select {
    font-size: calc(var(--type-base-size) - 1px);
    min-width: -webkit-max-content;
    min-width: max-content
}

.footer__section {
    padding: 30px 0;
    border-top: 1px solid;
    border-top-color: var(--color-border)
}

.site-footer .footer__section {
    border-top-color: var(--color-footer-border)
}

.site-footer:not(:has([data-type="menu"])) .footer__section {
    padding-top: 0;
    border-top-width: 0
}

@media only screen and (min-width:769px) {
    .footer__section--menus {
        padding-top: 50px
    }
}

@media only screen and (max-width:768px) {
    .footer__mobile-section {
        margin-left: calc(var(--page-width-padding) * -1);
        margin-right: calc(var(--page-width-padding) * -1);
        padding: 20px var(--page-width-padding) 0;
        border-top: 1px solid;
        border-top-color: var(--color-footer-border)
    }
}

.footer__title {
    margin-bottom: 16px
}

.footer__subscribe {
    font-weight: var(--type-header-weight);
    font-size: calc(var(--type-base-size) + 2px)
}

.footer__menu {
    margin: 0 0 20px;
    padding: 0;
    list-style: none
}

@media only screen and (max-width:768px) {
    .footer__menu {
        margin-bottom: 0
    }
}

.footer__menu li {
    margin: 0
}

.footer__menu a {
    display: inline-block;
    padding: 4px 0
}

.footer__menu .icon {
    margin-right: 10px
}

[dir='rtl'] .footer__menu .icon {
    margin-right: 0;
    margin-left: 10px
}

.footer__menu--underline a {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.footer__newsletter {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 20px
}

.footer__newsletter>* {
    padding: 0 20px
}

.footer__newsletter .newsletter__input-group {
    margin-bottom: 0
}

@media only screen and (max-width:768px) {
    .footer__newsletter {
        flex-wrap: wrap;
        text-align: center;
        justify-content: center
    }

    .footer__newsletter>* {
        padding: 0
    }

    .footer__newsletter .newsletter__input-group {
        max-width: none
    }

    .footer__newsletter .newsletter__input-group .btn {
        min-width: auto
    }

    .footer__newsletter form {
        width: 100%;
        margin: 20px auto 0
    }
}

.footer__blocks {
    display: flex;
    flex-wrap: wrap;
    /* justify-content: center; */
    gap: 120px
}

.footer__block {
    /* flex: 0 1 25%; */
    max-width: 210px
}

@media only screen and (max-width:768px) {
    .footer__blocks--mobile {
        display: flex;
        flex-wrap: wrap;
        justify-content: center
    }

    .footer__block,
    .footer__block--mobile {
        max-width: none;
        flex: 1 1 50%
    }

    .footer__block[data-type='payment'],
    .footer__block[data-type='contact'] {
        flex: 1 0 100%
    }

    :scope .multi-selectors--footer .multi-selectors__item {
        margin: 0
    }
}

.footer__social {
    margin: 0
}

form+.footer__social {
    margin-top: var(--gutter)
}

.footer__social li {
    display: inline-block;
    margin: 0 15px 15px 0
}

.footer__social a {
    display: block
}

.footer__social .icon {
    width: 21px;
    height: 21px
}

.footer__social .icon.icon--wide {
    width: 40px
}

.payment-icons {
    -webkit-user-select: none;
    user-select: none;
    cursor: default;
    margin-bottom: 20px
}

@media only screen and (max-width:768px) {
    .payment-icons {
        margin-bottom: 0
    }
}

.payment-icons li {
    cursor: default;
    margin: 0 8px 4px 0
}

.footer__base-links {
    font-size: 13px
}

.footer__base-links a,
.footer__base-links span {
    display: inline-block;
    padding: 2px 20px 2px 0
}

.footer__base-links a {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

@media only screen and (max-width:768px) {
    .footer-promotions .grid__item {
        margin-bottom: 32px
    }

    .footer-promotions .grid__item:last-child {
        margin-bottom: 0
    }
}

.footer-promotion {
    width: 100%;
    text-align: center;
    padding: 22px;
    border-radius: var(--roundness);
    overflow: hidden
}

@media only screen and (min-width:769px) {
    .footer-promotion {
        padding: 30px
    }
}

.footer-promotion .scheme-image {
    border-radius: calc(var(--roundness) + 2px)
}

.footer-promotion[class*='color-scheme-']:not(.color-scheme-none) .btn {
    border: 0
}

.footer__grid-image {
    display: block;
    text-align: center;
    margin-bottom: 17px
}

@media only screen and (min-width:769px) {
    .footer__grid-image {
        margin-bottom: 20px
    }
}

.footer__grid-image img {
    display: block
}

.color-scheme-none.footer-promotion {
    border: 1px solid;
    border-color: var(--color-border)
}

.giftcard-header {
    padding: calc(var(--gutter) * 2) 0;
    font-size: 1em;
    text-align: center
}

.giftcard-header .site-header__logo a {
    display: block;
    margin: 0 auto;
    color: var(--color-text-body)
}

.section-header {
    margin-bottom: var(--section-header-bottom)
}

.section-header select {
    display: inline-block;
    vertical-align: middle
}

.section-header--flush {
    margin-bottom: 0
}

.section-header--with-link {
    display: flex;
    align-items: center
}

.section-header--with-link select {
    flex: 0 1 auto
}

.section-header__title {
    margin-bottom: 0
}

.section-header--with-link .section-header__title {
    flex: 1 1 auto
}

.section-header__title a {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.section-header__link {
    flex: 0 1 auto;
    margin-top: calc(var(--gutter) / 2);
    font-weight: var(--type-header-weight);
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px;
    white-space: nowrap
}

.section-header--with-link .section-header__link {
    margin-top: 0
}

@media only screen and (max-width:768px) {
    .section-header__link {
        margin-left: 10px
    }
}

.section-header--404 {
    padding-top: 80px
}

.section-header select {
    margin: 10px 0
}

.section-header p {
    margin: 10px 0
}

header-section {
    display: block
}

[dir='rtl'] :scope .site-header__logo {
    margin-left: 20px
}

.site-header__drawers {
    height: 0;
    overflow: visible
}

@media only screen and (max-width:768px) {
    .site-header__drawers {
        padding: 0
    }
}

.site-header__drawers-container {
    position: relative
}

[data-logo-align='center'] .header-item--search predictive-search:not(.is-active) {
    transform: translateX(-50px);
    transition: transform 0.2s ease
}

.header-wrapper--compressed [data-logo-align='center'] .header-item--search predictive-search:not(.is-active) {
    transform: translateX(0)
}

.site-header__search-btn {
    padding: 0 8px
}

.site-header__search-btn--cancel {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    right: -50px;
    color: var(--color-nav-text)
}

.site-header__search-btn--cancel:hover {
    color: var(--color-nav-text)
}

.site-header__search-container .site-header__search-btn--cancel {
    padding-right: 5px
}

.site-header__mobile-nav {
    left: 0;
    right: 0;
    overflow-x: hidden
}

.header-layout {
    display: flex;
    align-items: center
}

[data-layout='center'] {
    align-items: center
}

.header-item {
    display: flex;
    align-items: center;
    flex: 1 1 auto
}

.header-item--compress-nav {
    visibility: hidden;
    flex: 0 0 auto;
    width: 0;
    opacity: 0;
    transition: all 0.3s cubic-bezier(.18, .77, .58, 1);
    overflow: hidden
}

.header-wrapper--compressed .header-item--compress-nav {
    visibility: visible;
    opacity: 1;
    width: 50px;
    transform: translateX(calc(var(--site-nav-icon-padding) * -1))
}

.header-item--logo {
    flex: 0 0 auto;
    word-break: break-all
}

.header-item--search {
    flex: 1 1 100%;
    max-width: 475px
}

[data-layout='center'] .header-item--search {
    max-width: none
}

[data-layout='center'] .header-item--search form {
    max-width: 320px
}

.header-item--search .site-nav__compress-menu {
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(.18, .77, .58, 1);
    width: 50px
}

.header-wrapper--compressed .header-item--search .site-nav__compress-menu {
    visibility: visible;
    opacity: 1;
    transform: translateX(calc(var(--site-nav-icon-padding) * -1))
}

[dir='rtl'] .header-wrapper--compressed .header-item--search .site-nav__compress-menu {
    transform: translateX(0)
}

.header-item--icons {
    justify-content: flex-end;
    flex: 1 1 auto
}

@media only screen and (min-width:769px) {
    .header-layout[data-logo-align='center'] .header-item--logo {
        margin: 0 30px
    }
}

.header-layout[data-logo-align='center'] .header-item--navigation,
.header-layout[data-logo-align='center'] .header-item--icons,
.header-layout[data-logo-align='center'] .header-item--search {
    flex: 1 1 33%
}

@media only screen and (min-width:769px) {
    .header-layout[data-logo-align='left'] .site-header__logo {
        margin-right: 30px
    }
}

.header-item--left .site-nav {
    margin-left: calc(var(--site-nav-icon-padding) * -1)
}

@media only screen and (max-width:768px) {
    .header-item--left .site-nav {
        margin-left: calc(var(--site-nav-item-padding) / -2)
    }
}

.header-item--icons .site-nav {
    margin-right: calc(var(--site-nav-icon-padding) * -1)
}

@media only screen and (max-width:768px) {
    .header-item--icons .site-nav {
        margin-right: calc(var(--site-nav-item-padding) / -2)
    }
}

@media only screen and (max-width:768px) {
    .header-wrapper:not(.header-wrapper--compressed) [data-nav='below'] .js-search-header {
        display: none
    }
}

.mobile-nav-trigger path,
.site-nav__compress-menu path {
    transition: all 0.3s cubic-bezier(.18, .77, .58, 1)
}

.mobile-nav-trigger.is-active path:nth-child(1),
.site-nav__compress-menu.is-active path:nth-child(1) {
    transform: rotate(45deg);
    transform-origin: 20% 30%
}

.mobile-nav-trigger.is-active path:nth-child(2),
.site-nav__compress-menu.is-active path:nth-child(2) {
    opacity: 0
}

.mobile-nav-trigger.is-active path:nth-child(3),
.site-nav__compress-menu.is-active path:nth-child(3) {
    transform: rotate(-45deg);
    transform-origin: 15% 66%
}

[data-layout='left-center'] .header-item--logo,
[data-layout='left-center'] .header-item--icons {
    flex: 0 0 200px;
    max-width: 50%
}

@media only screen and (min-width:769px) {

    [data-layout='left-center'] .header-item--logo,
    [data-layout='left-center'] .header-item--icons {
        min-width: 0
    }
}

.toolbar-section {
    position: relative;
    z-index: var(--z-index-toolbar)
}

.header-section {
    position: sticky;
    top: 0;
    z-index: var(--z-index-header);
    padding-bottom: var(--header-padding-bottom);
    pointer-events: none
}

.header-section+.toolbar-section {
    z-index: calc(var(--z-index-header) - 1)
}

.header-section--overlay {
    padding-bottom: 0
}

[data-section-type='header'] {
    pointer-events: auto
}

.site-header {
    position: relative
}

.site-header--password {
    color: var(--color-nav-text)
}

.site-header--password a,
.site-header--password a:hover {
    color: var(--color-nav-text)
}

.is-light .site-header--password {
    color: var(--color-sticky-nav-links)
}

.is-light .site-header--password a,
.is-light .site-header--password a:hover {
    color: var(--color-sticky-nav-links)
}

.site-header__element {
    position: relative;
    background-color: var(--color-nav);
    z-index: var(--z-index-header)
}

.is-light .site-header__element {
    background: #fff0;
    box-shadow: none
}

.site-header__element--top {
    padding: 5px 0;
    transition: background-color 0.2s ease
}

@media only screen and (max-width:768px) {
    .site-header__element--top {
        padding: 0
    }
}

.site-header__element--sub {
    padding: 0;
    z-index: var(--z-index-header-bottom-row);
    transition: transform 0.2s cubic-bezier(.18, .77, .58, 1), background-color 0.2s cubic-bezier(.18, .77, .58, 1)
}

.header-wrapper--compressed .site-header__element--sub {
    transform: translateY(-100%);
    display: none
}

.header-wrapper--compressed .site-header__element--sub.is-active {
    display: block;
    transform: translateY(0)
}

.header-wrapper--init .site-header__element--sub {
    position: absolute;
    left: 0;
    right: 0
}

.site-header__element--sub[data-type='search'] .page-width {
    padding-bottom: 20px
}

@media screen and (min-width:700px) and (max-height:550px) {
    .header-section {
        position: static
    }
}

.site-header__logo {
    position: relative;
    margin: calc(var(--gutter) / 3) 0;
    display: block;
    font-size: 24px;
    z-index: calc(var(--z-index-header) + 1)
}

@media only screen and (min-width:769px) {
    .text-center .site-header__logo {
        padding-right: 0;
        margin: calc(var(--gutter) / 3) auto
    }
}

.header-layout[data-logo-align='center'] .site-header__logo {
    margin-left: auto;
    margin-right: auto;
    text-align: center
}

@media only screen and (max-width:768px) {
    .header-layout[data-logo-align='center'] .site-header__logo {
        text-align: left;
        margin-left: 0;
        margin-right: 0
    }
}

.site-header__logo a {
    max-width: 100%
}

.site-header__logo a,
.site-header__logo a:hover {
    -webkit-text-decoration: none;
    text-decoration: none
}

.site-header__logo img {
    display: block;
    object-fit: cover
}

.header-layout[data-logo-align='center'] .site-header__logo img {
    margin-left: auto;
    margin-right: auto
}

.site-header__logo-link {
    display: flex;
    align-items: center;
    color: var(--color-nav-text);
    word-break: break-word
}

.site-header__logo-link:hover {
    color: var(--color-nav-text)
}

@media only screen and (max-width:768px) {
    .site-header__logo-link {
        margin: 0 auto
    }
}

.header-wrapper--overlay {
    background: none
}

header-section[data-section-index='2'] .header-wrapper--overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0
}

.header-wrapper--overlay.is-light:after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: -50px;
    background: linear-gradient(180deg, rgb(0 0 0 / .4) 0%, rgb(0 0 0 / .3) 50%, transparent);
    pointer-events: none
}

.is-light .site-header__logo .logo--has-inverted {
    opacity: 0;
    visibility: hidden;
    overflow: hidden;
    height: 0
}

.site-header__logo .logo--inverted {
    opacity: 0;
    visibility: hidden;
    overflow: hidden;
    height: 0
}

.is-light .site-header__logo .logo--inverted {
    opacity: 1;
    visibility: visible;
    height: auto
}

@media only screen and (min-width:769px) {
    .site-header__logo {
        text-align: left
    }
}

.site-header__logo a {
    color: var(--color-nav-text)
}

.is-light .site-header__logo a {
    color: var(--color-sticky-nav-links)
}

.is-light .site-header__logo a:hover {
    color: var(--color-sticky-nav-links)
}

.site-nav {
    margin: 0
}

.site-nav__link {
    display: inline-block;
    vertical-align: middle;
    -webkit-text-decoration: none;
    text-decoration: none;
    padding: var(--site-nav-item-padding-top-bottom) var(--site-nav-item-padding);
    white-space: nowrap;
    color: var(--color-nav-text)
}

.site-nav__link:hover {
    color: var(--color-nav-text)
}

.is-light .site-nav__link {
    color: var(--color-sticky-nav-links)
}

.is-light .site-nav__link:hover {
    color: var(--color-sticky-nav-links)
}

.site-nav__link .icon-chevron-down {
    margin-left: 5px
}

@media only screen and (max-width:959px) {
    .site-nav__link {
        padding: var(--site-nav-item-padding-top-bottom) var(--site-nav-item-padding)
    }
}

.site-nav__icons {
    display: flex;
    align-items: center
}

.site-nav__icons>.site-nav__link--icon {
    flex: 0 0 auto
}

.cart-open .site-nav__icons {
    display: none
}

.site-nav__link--cart .icon,
.site-nav__link--cart .cart-link {
    pointer-events: none
}

.site-nav__link--icon {
    display: flex;
    align-items: center;
    padding-left: var(--site-nav-icon-padding);
    padding-right: var(--site-nav-icon-padding)
}

@media only screen and (max-width:768px) {
    .site-nav__link--icon {
        padding-left: calc(var(--site-nav-item-padding) / 2);
        padding-right: calc(var(--site-nav-item-padding) / 2)
    }

    .site-nav__link--icon+.site-nav__link--icon {
        margin-left: -4px
    }
}

.site-nav__link--icon .icon {
    width: 27px;
    height: 27px
}

.site-nav__link--icon .icon.icon-user {
    position: relative;
    top: 1px
}

@media only screen and (max-width:768px) {
    .mobile-nav-open .site-nav__link:not(.mobile-nav-trigger) {
        display: none
    }
}

.hero__media-container {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%
}

.hero__media {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: var(--z-index-hero-image)
}

.hero.video-parent-section {
    display: block
}

.hero__media iframe,
.hero__media video {
    width: 100%;
    height: 100%;
    pointer-events: none
}

.video-interactable .hero__media iframe,
.video-interactable .hero__media video {
    pointer-events: auto
}

.video-parent-section.loading .hero__media iframe,
.video-parent-section.loading .hero__media video {
    opacity: .01
}

.video-parent-section.loaded .hero__media iframe,
.video-parent-section.loaded .hero__media video {
    opacity: 1;
    animation: zoom-fade 1s cubic-bezier(.26, .54, .32, 1) 0s forwards;
    transition: none
}

.hero__media video {
    position: relative;
    object-fit: cover;
    font-family: 'object-fit: cover'
}

.hero__media iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 300%;
    left: -100%;
    max-width: none
}

@media screen and (min-width:1140px) {
    .hero__media iframe {
        width: 100%;
        height: 300%;
        left: auto;
        top: -100%
    }
}

@media only screen and (max-width:768px) {
    .hotspots-section .index-section {
        margin-bottom: 10px
    }
}

.hotspots-section .hotspots-wrapper {
    display: flex;
    flex-wrap: wrap
}

.hotspots-section .hotspots-wrapper.is-reverse {
    flex-direction: row-reverse
}

.hotspots__title {
    width: 100%;
    padding-top: 1.5rem
}

.hotspots {
    position: relative;
    width: 70%
}

@media only screen and (max-width:768px) {
    .hotspots {
        width: 100%
    }
}

.hotspots .grid__image-ratio img {
    opacity: 1;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover
}

[data-animate_images='true'] .hotspots .grid__image-ratio img {
    opacity: 1
}

.hotspots__buttons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
    background-color: #fff0
}

.hotspot__button {
    padding: 10px;
    border-radius: 50%;
    position: absolute;
    line-height: 0;
    transform: translate(-50%, -50%)
}

@media only screen and (max-width:768px) {
    .hotspot__button {
        padding: 6px
    }
}

.hotspot__button:hover .hotspot__button-content {
    opacity: 1;
    visibility: visible;
    pointer-events: auto
}

.hotspot__button-content {
    color: var(--color-text-body);
    background-color: var(--color-body);
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    padding: 1rem;
    position: absolute;
    top: calc(100% + 1rem);
    left: 50%;
    transform: translateX(-50%);
    transition: opacity 0.3s ease-in-out;
    min-width: 5rem;
    border-radius: 5px;
    box-shadow: 3px 3px 10px 3px rgb(0 0 0 / .2)
}

.hotspot__button-content p,
.hotspot__button-content span {
    white-space: nowrap;
    margin-bottom: 0
}

.hotspot__button-content:before {
    position: absolute;
    top: -10px;
    left: 50%;
    content: '';
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 5px 10px 5px;
    transform: translateX(-50%);
    border-color: #fff0 #fff0 var(--color-body) #fff0
}

.hotspot__button-content .content__prices {
    display: flex;
    flex-wrap: nowrap
}

.hotspots__content {
    width: 30%;
    padding: 24px;
    display: flex;
    align-items: center
}

.page-width .hotspots__content {
    padding-right: 0;
    padding-left: 40px
}

@media only screen and (max-width:768px) {
    .page-width .hotspots__content {
        padding-left: 0
    }
}

.page-width.is-reverse .hotspots__content {
    padding-left: 0;
    padding-right: 40px
}

@media only screen and (max-width:768px) {
    .page-width.is-reverse .hotspots__content {
        padding-right: 20px
    }
}

@media only screen and (max-width:768px) {
    .hotspots__content {
        width: 100%;
        padding: 1rem 20px 0
    }
}

.hotspot-content__block {
    display: none;
    max-height: 0;
    width: 100%;
    animation: fade-in 0.5s ease 0s forwards;
    position: sticky;
    top: 0
}

.modal-open .hotspot-content__block,
.modal-closing .hotspot-content__block {
    animation: none;
    position: static
}

@media only screen and (max-width:768px) {
    .hotspot-content__block {
        align-items: center;
        position: relative;
        padding-top: 0
    }
}

.hotspot-content__block.is-active {
    display: block;
    max-height: initial
}

.hotspot-content__block .grid-product__image-wrap {
    margin: 0
}

.hotspot-content__block .grid__item {
    display: block;
    float: none;
    padding: 0
}

.hotspot-content__block .grid-product__tags {
    margin-left: 0
}

@media only screen and (max-width:768px) {
    .hotspot-content__block .grid-product__tag {
        right: auto;
        left: 0
    }
}

.hotspot-content__block .grid-item__meta,
.hotspot-content__block .grid-product__meta {
    padding-top: 10px
}

@media only screen and (max-width:768px) {

    .hotspot-content__block .grid-item__meta,
    .hotspot-content__block .grid-product__meta {
        display: flex;
        justify-content: center;
        flex-direction: column;
        flex-wrap: wrap;
        padding-left: 10px;
        padding-top: 0;
        text-align: left
    }

    .hotspot-content__block .grid-item__meta .grid-item__meta-main,
    .hotspot-content__block .grid-product__meta .grid-item__meta-main,
    .hotspot-content__block .grid-item__meta .grid-item__meta-secondary,
    .hotspot-content__block .grid-product__meta .grid-item__meta-secondary {
        width: 100%;
        flex: none
    }
}

@media only screen and (max-width:768px) {
    .hotspot-content__block .grid-product {
        padding-right: 0;
        padding-left: 0
    }

    .hotspot-content__block .quick-add-btn {
        display: none
    }

    .hotspot-content__block .grid__item-image-wrapper {
        display: flex
    }

    .hotspot-content__block .grid__item-image-wrapper .grid-product__image-mask {
        flex: 0 0 30%
    }

    .hotspot-content__block .grid-item__link,
    .hotspot-content__block .grid-product__link {
        display: flex;
        flex-wrap: nowrap
    }

    [data-grid-style='grey-round'] .hotspot-content__block .grid-item__link {
        border-radius: 10px 0 0 10px
    }

    [data-grid-style='white-round'] .hotspot-content__block .grid-item__link {
        border-radius: 10px 0 0 10px
    }

    .hotspot-content__block .grid-product__image-mask,
    .hotspot-content__block .grid-product__image-wrap,
    .hotspot-content__block .product-slider {
        width: 30%
    }

    .hotspot-content__block .grid-product__actions {
        right: initial;
        left: 10px;
        top: 10px
    }
}

.comparison {
    position: relative;
    display: block;
    width: 100%;
    background: #222;
    overflow: hidden
}

.comparison__draggable {
    position: absolute;
    top: 0;
    height: 100%;
    width: 50%;
    overflow: hidden;
    z-index: 2
}

.comparison__image {
    width: 100%;
    height: 100%;
    max-width: none;
    object-fit: cover;
    display: block;
    -webkit-user-select: none;
    user-select: none;
    opacity: 1;
    animation: none
}

.comparison__image-wrapper {
    width: 100%;
    height: 100%
}

.comparison__button {
    width: 64px;
    height: 64px;
    position: absolute;
    border-radius: 50%;
    left: 50%;
    top: 50%;
    transform: translate(-32px, -50%);
    cursor: pointer;
    z-index: 3;
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    border: 3px solid #fff;
    padding: 0
}

.comparison--style-minimal .comparison__button {
    border: 0;
    background: #fff0;
    width: auto;
    transform: translate(-56px, -50%)
}

.comparison--style-minimal .comparison__button svg {
    margin: 0 20px
}

@supports (-webkit-touch-callout:none) {
    .comparison--style-minimal .comparison__button svg {
        position: absolute
    }

    .comparison--style-minimal .comparison__button svg.icon-chevron-left {
        left: 2px;
        width: 9px
    }

    .comparison--style-minimal .comparison__button svg.icon-chevron-right {
        right: 2px;
        width: 9px
    }
}

@media only screen and (max-width:768px) {
    .comparison--style-classic .comparison__button {
        width: 48px;
        height: 48px;
        transform: translate(-24px, -50%)
    }
}

.comparison__button svg {
    width: 12px;
    height: auto;
    pointer-events: none;
    margin: 0 5px
}

.comparison__button::before {
    content: '';
    width: 2px;
    position: absolute;
    background-color: #fff !important
}

.comparison__button::after {
    content: '';
    width: 2px;
    height: 50%;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 0);
    background-color: #fff !important
}

.logo-bar {
    text-align: center
}

.logo-bar .product-grid:not([data-view='6-2']) .grid-item {
    flex-basis: auto
}

.logo-bar--layout-slider .new-grid {
    flex-wrap: nowrap;
    overflow-x: scroll;
    scroll-snap-type: x mandatory;
    scrollbar-width: none
}

.logo-bar--layout-slider .new-grid::-webkit-scrollbar {
    width: 0;
    background: #fff0
}

.logo-bar--layout-slider .grid-item {
    scroll-snap-align: start
}

.logo-bar--layout-carousel .new-grid {
    display: flex;
    flex-wrap: nowrap;
    animation-play-state: running;
    animation: scrolling-carousel calc(var(--move-speed) * 0.071) linear infinite
}

@media (medium-down) {
    .logo-bar--layout-carousel .new-grid {
        animation: scrolling-carousel calc(var(--move-speed) * 0.038) linear infinite
    }
}

@media only screen and (max-width:768px) {
    .logo-bar--layout-carousel .new-grid {
        animation: scrolling-carousel calc(var(--move-speed) * 0.019) linear infinite
    }
}

.logo-bar--layout-carousel .new-grid:hover,
.logo-bar--layout-carousel .new-grid :focus {
    animation-play-state: paused
}

.logo-bar--layout-carousel.logo-bar--direction-right .new-grid {
    animation-name: scrolling-carousel-right
}

@keyframes scrolling-carousel {
    0% {
        transform: translateX(0)
    }

    100% {
        transform: translateX(-100%)
    }
}

@keyframes scrolling-carousel-right {
    0% {
        transform: translateX(-50%)
    }

    100% {
        transform: translateX(0)
    }
}

.logo-bar__item {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    padding: 25px 30px;
    background-color: var(--color-body)
}

[data-grid-style*='white'] .logo-bar__item {
    background-color: #fff
}

[data-grid-style='white-round'] .logo-bar__item {
    border-radius: var(--product-radius)
}

.logo-bar__item-svg {
    padding: 25px 30px
}

.logo-bar__image {
    display: block;
    margin: 0 auto;
    object-fit: cover;
    height: auto;
    width: auto
}

.logo-bar__link {
    display: block
}

.cart__discounts {
    margin-top: -10px;
    margin-bottom: 10px
}

@media only screen and (min-width:769px) {
    .cart__page {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: flex-start;
        max-width: 1200px;
        margin: 0 auto
    }

    .cart__page-col:first-child {
        flex: 1 1 65%;
        padding-right: 100px
    }

    .cart__page-col:last-child {
        --z-index-overlay: -1;
        flex: 0 1 35%;
        align-self: flex-start;
        position: sticky;
        top: 130px;
        padding: 30px
    }

    .cart__page-col:last-child:after {
        background-color: var(--color-text-body);
        opacity: .03
    }

    .cart__page-col:last-child input,
    .cart__page-col:last-child textarea {
        background-color: var(--color-body)
    }

    .medium-up--overlay::after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: var(--z-index-overlay, auto)
    }
}

.cart__item-sub {
    flex: 1 1 100%;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.cart__item-row {
    margin-bottom: 20px
}

.cart__item-row:last-child {
    margin-bottom: 0
}

.cart__item-row.cart-recommendations {
    margin-bottom: 20px
}

.cart__item-row--footer {
    padding: 0 60px
}

.cart__checkout,
.cart__continue {
    width: 100%
}

.site-header__cart {
    right: 0
}

@media only screen and (min-width:769px) {
    .site-header__cart {
        max-width: 450px
    }
}

.cart__checkout-wrapper .additional-checkout-buttons,
.cart__checkout-wrapper .cart__continue {
    margin-top: 12px
}

.site-header__cart .additional-checkout-buttons {
    margin: 10px 0
}

.site-header__cart .additional-checkout-buttons [data-shopify-buttoncontainer] {
    justify-content: center
}

.site-header__cart .additional-checkout-buttons [data-shopify-buttoncontainer]>* {
    height: auto !important
}

.additional-checkout-buttons div[role='button'] {
    border-radius: var(--button-radius) !important
}

.cart__discount {
    color: var(--color-text-savings)
}

.cart__item--subtotal {
    font-weight: var(--type-header-weight);
    font-size: calc(var(--type-base-size) + 2px)
}

.cart__terms {
    display: flex;
    align-items: center;
    justify-content: center
}

.cart__terms label {
    margin-bottom: 0
}

iframe.zoid-component-frame {
    z-index: 1 !important
}

.site-header__cart {
    padding: 0;
    overflow: hidden
}

section-main-content {
    display: block
}

@media only screen and (min-width:769px) {
    section-main-content {
        padding-top: 60px
    }
}

.rte--collection-desc {
    padding: 45px 0;
    margin-bottom: 0
}

.template-giftcard,
.template-giftcard body {
    background: var(--color-body)
}

.template-giftcard a,
.template-giftcard body a {
    -webkit-text-decoration: none;
    text-decoration: none
}

.template-giftcard .page-width {
    max-width: 588px
}

.template-giftcard .shop-url {
    display: none
}

.giftcard__border {
    padding: 1.5em;
    box-shadow: 0 10px 30px rgb(0 0 0 / .3)
}

.giftcard__content {
    background-color: var(--color-body);
    color: var(--color-text-body)
}

.giftcard__header {
    padding: calc(var(--gutter) / 2)
}

.giftcard__title {
    float: left;
    margin-bottom: 0
}

.giftcard__tag {
    display: block;
    float: right;
    background-color: var(--color-text-body);
    border: 1px solid #fff0;
    color: var(--color-body);
    padding: calc(var(--gutter) / 3);
    border-radius: 4px;
    font-size: .75em;
    text-transform: uppercase;
    letter-spacing: .05em;
    line-height: 1
}

.giftcard__tag--active {
    background: #fff0;
    color: var(--color-text-body);
    border: 1px solid;
    border-color: var(--color-border)
}

.giftcard__wrap {
    position: relative;
    margin: calc(var(--gutter) / 2) calc(var(--gutter) / 2) var(--gutter)
}

.giftcard__wrap img {
    position: relative;
    display: block;
    border-radius: 10px;
    z-index: 2
}

.giftcard__code {
    position: absolute;
    bottom: var(--gutter);
    text-align: center;
    width: 100%;
    z-index: 50
}

.giftcard__code--medium {
    font-size: .875em
}

.giftcard__code--small {
    font-size: .75em
}

.giftcard__code__inner {
    display: inline-block;
    vertical-align: baseline;
    background-color: #fff;
    padding: .5em;
    border-radius: 4px;
    max-width: 450px;
    box-shadow: 0 0 0 1px rgb(0 0 0 / .1)
}

.giftcard__code--small .giftcard__code__inner {
    overflow: auto
}

.giftcard__code__text {
    font-weight: 400;
    font-size: 1.875em;
    text-transform: uppercase;
    border-radius: 2px;
    border: 1px dashed;
    border-color: var(--color-border);
    padding: .4em .5em;
    display: inline-block;
    vertical-align: baseline;
    color: var(--color-text-body);
    line-height: 1
}

.disabled .giftcard__code__text {
    color: #999;
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.giftcard__amount {
    position: absolute;
    top: 0;
    right: 0;
    color: #fff;
    font-size: 2.75em;
    line-height: 1.2;
    padding: calc(var(--gutter) / 2);
    z-index: 50
}

.giftcard__amount strong {
    display: block;
    text-shadow: 3px 3px 0 rgb(0 0 0 / .1)
}

.giftcard__amount--medium {
    font-size: 2em
}

.tooltip {
    display: block;
    position: absolute;
    top: -50%;
    right: 50%;
    margin-top: 16px;
    z-index: 3;
    color: #fff;
    text-align: center;
    white-space: nowrap
}

.tooltip:before {
    content: '';
    display: block;
    position: absolute;
    left: 100%;
    bottom: 0;
    width: 0;
    height: 0;
    margin-left: -5px;
    margin-bottom: -5px;
    border-left: 8px solid #fff0;
    border-right: 8px solid #fff0;
    border-top: 5px solid #333;
    border-top: 5px solid rgb(51 51 51 / .9)
}

.tooltip__label {
    display: block;
    position: relative;
    right: -50%;
    border: none;
    border-radius: 4px;
    background: #333;
    background: rgb(51 51 51 / .9);
    min-height: 14px;
    font-weight: 400;
    font-size: 12px;
    -webkit-text-decoration: none;
    text-decoration: none;
    line-height: 16px;
    text-shadow: none;
    padding: .5em .75em;
    margin-left: .25em
}

.tooltip__label small {
    text-transform: uppercase;
    letter-spacing: .1em;
    color: #b3b3b3;
    font-size: .875em
}

.giftcard__instructions {
    text-align: center;
    margin: 0 calc(var(--gutter) / 2) var(--gutter)
}

.giftcard__actions {
    position: relative;
    text-align: center;
    overflow: hidden;
    padding-bottom: 1em
}

.template-giftcard .action-link {
    position: absolute;
    left: calc(var(--gutter) / 2);
    top: 50%;
    font-size: .875em;
    font-weight: var(--type-header-weight);
    display: block;
    padding-top: 4px;
    text-transform: uppercase;
    letter-spacing: .2em;
    margin-top: -10px
}

.template-giftcard .action-link:hover,
.template-giftcard .action-link:focus {
    color: var(--color-text-body)
}

.template-giftcard .action-link__print {
    display: inline-block;
    vertical-align: baseline;
    width: 17px;
    height: 17px;
    vertical-align: middle;
    margin-right: 10px;
    opacity: 1;
    background-image: url(//cdn.shopify.com/s/assets/gift-card/icon-print-164daa1ae32d10d1f9b83ac21b6f2c70.png);
    background-repeat: no-repeat;
    background-position: 0 0
}

.giftcard__footer {
    text-align: center;
    padding: calc(var(--gutter) * 2) 0
}

#QrCode img {
    padding: var(--gutter);
    border: 1px solid;
    border-color: var(--color-border);
    border-radius: 4px;
    margin: 0 auto var(--gutter)
}

@media only screen and (max-width:768px) {
    .giftcard {
        font-size: 12px
    }

    .giftcard-header {
        padding: var(--gutter) 0
    }

    .header-logo {
        font-size: 2em
    }

    .giftcard__border {
        padding: calc(var(--gutter) / 2)
    }

    .giftcard__actions {
        padding: calc(var(--gutter) / 2)
    }

    .giftcard__actions .btn {
        width: 100%;
        padding-left: 0;
        padding-right: 0
    }

    .template-giftcard .action-link {
        display: none
    }
}

@media screen and (max-width:400px) {
    .giftcard__amount strong {
        text-shadow: 2px 2px 0 rgb(0 0 0 / .1)
    }

    .giftcard__wrap:before,
    .giftcard__wrap:after {
        display: none
    }

    .giftcard__code {
        font-size: .75em
    }

    .giftcard__code--medium {
        font-size: .65em
    }

    .giftcard__code--small {
        font-size: .55em
    }
}

@media screen and (max-height:800px) {
    .header-logo img {
        max-height: 90px
    }
}

@media print {
    @page {
        margin: .5cm
    }

    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3
    }

    h2,
    h3 {
        page-break-after: avoid
    }

    html,
    body {
        background-color: #fff
    }

    .giftcard-header {
        padding: 10px 0
    }

    .giftcard__content,
    .giftcard__border {
        border: 0 none
    }

    .site-header__logo-link img:nth-child(2),
    .giftcard__actions,
    .giftcard__wrap:before,
    .giftcard__wrap:after,
    .tooltip,
    .add-to-apple-wallet {
        display: none
    }

    .giftcard__title {
        float: none;
        text-align: center
    }

    .giftcard__code__text {
        color: #555
    }

    .template-giftcard .shop-url {
        display: block
    }

    .template-giftcard .logo {
        color: #58686f
    }
}

section-main-search {
    display: block
}

.main-search__content {
    padding-top: 5px
}

@media only screen and (min-width:769px) {
    .main-search__content {
        padding-top: 60px
    }
}

.search-bar {
    max-width: 100%
}

.search-bar--page {
    max-width: 300px;
    margin-top: calc(var(--gutter) / -2)
}

.search-bar .icon {
    width: 24px;
    height: 24px;
    vertical-align: middle
}

.map-section {
    display: block;
    position: relative;
    height: 650px;
    width: 100%;
    overflow: hidden
}

@media only screen and (min-width:769px) {
    .map-section {
        height: 500px
    }
}

.map-section:not(.map-section--no-overlay) {
    display: grid
}

.map-section:not(.map-section--no-overlay) .map-section__content:not([class*='color-scheme-']) {
    background-color: var(--color-body);
    color: var(--color-text-body)
}

@media only screen and (max-width:768px) {
    .map-section:not(.map-section--no-overlay) .content-over-media {
        --content-over-media-column-gap: 20px;
        --content-over-media-row-gap: 20px
    }

    .map-section:not(.map-section--no-overlay) .content-over-media .content-over-media__content {
        width: 100%
    }

    .map-section:not(.map-section--no-overlay) .content-over-media .map-section__content {
        max-width: none
    }
}

@media only screen and (max-width:768px) {
    .map-section--no-overlay {
        height: auto
    }

    .map-section--no-overlay .map-section__content {
        max-width: none
    }
}

@media only screen and (min-width:769px) {
    .map-section--no-overlay {
        display: flex
    }

    .map-section--no-overlay .map-section__content {
        min-width: 380px;
        align-content: center
    }
}

.map-section__map {
    position: relative;
    width: 100%
}

@media only screen and (max-width:768px) {
    .map-section__map {
        aspect-ratio: 5 / 4
    }
}

.map-section__content {
    padding: var(--gutter);
    max-width: 380px;
    border-radius: var(--roundness);
    overflow: hidden
}

.map-section--no-overlay .map-section__content {
    padding-top: calc(var(--gutter) * 1.25);
    padding-bottom: calc(var(--gutter) * 1.25)
}

.map-section--no-overlay .map-section__content[layout='right'] {
    order: 1
}

.newsletter-section .errors {
    margin-left: auto;
    margin-right: auto;
    max-width: 520px
}

.newsletter-container {
    margin: 0 auto !important;
    padding: 0 !important
}

.newsletter-section {
    display: flex;
    gap: 40px;
    align-items: center;
    padding: 60px 0
}

@media only screen and (max-width:768px) {
    .newsletter-section {
        flex-direction: column;
        padding: 30px 0
    }
}

.newsletter-section--image-left {
    flex-direction: row-reverse
}

@media only screen and (max-width:768px) {
    .newsletter-section--image-left {
        flex-direction: column-reverse
    }
}

.newsletter-section__content {
    flex: 100%;
    max-width: 800px;
    padding-left: 20px;
    margin: 0 auto
}

.newsletter-section--no-image .newsletter-section__content {
    flex: 100%
}

.newsletter-section__content form {
    display: flex;
    width: 100%
}

.text-left .newsletter-section__content form {
    justify-content: flex-start
}

.text-center .newsletter-section__content form {
    justify-content: center
}

.text-right .newsletter-section__content form {
    justify-content: flex-end
}

.newsletter-section__content .newsletter__input-group {
    margin: 0
}

.newsletter-section__image {
    width: 100%
}

.newsletter-section__image img {
    opacity: 1;
    animation: none;
    display: block;
    margin: 0 auto;
    object-fit: cover;
    height: auto
}

.newsletter-section__image--33 {
    flex: 33.333% 0 0
}

.newsletter-section__image--50 {
    flex: 50% 0 0
}

@media only screen and (max-width:768px) {
    .newsletter-section__image--50 {
        max-width: 100%
    }
}

.newsletter-section__image--66 {
    flex: 66.666% 0 0
}

@media only screen and (max-width:768px) {
    .newsletter-section__image--66 {
        max-width: 100%
    }
}

.pagination {
    margin: calc(var(--gutter) * 2) 0;
    text-align: center
}

.pagination>span {
    display: inline-block;
    vertical-align: middle;
    line-height: 1
}

.pagination a {
    display: inline-block
}

.pagination a:not(.btn),
.pagination .page.current {
    padding: 8px 12px
}

.pagination .page.current {
    opacity: .3
}

.pagination .btn {
    transition: transform 0.15s ease-out
}

.pagination .btn:hover {
    transform: scale(1.08)
}

.pagination .btn .icon {
    width: 13px;
    height: 13px
}

.modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded {
    padding: 0
}

@media only screen and (max-width:768px) {
    .modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded {
        padding: 0
    }
}

.modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup {
    display: flex;
    max-width: 800px;
    min-width: 650px
}

@media only screen and (max-width:768px) {
    .modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup {
        max-width: none;
        min-width: 0
    }
}

.modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup.newsletter-popup--image-reversed {
    flex-direction: row-reverse
}

.modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup__image-wrapper {
    position: relative;
    width: 50%
}

@media only screen and (max-width:768px) {
    .modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup__image-wrapper {
        display: none
    }
}

.modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute
}

.modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup__content {
    padding: 40px;
    width: 50%
}

@media only screen and (max-width:768px) {
    .modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup__content {
        padding: 40px;
        width: 100%
    }
}

.modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .form__submit--large {
    display: none
}

.modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .form__submit--small {
    display: block
}

.newsletter-popup {
    position: relative;
    margin: 0 auto;
    max-width: 520px;
    text-align: center
}

.newsletter-popup .h2 {
    margin-bottom: 0
}

@media only screen and (min-width:769px) {
    .newsletter-popup .h2 {
        margin-bottom: 0
    }
}

.newsletter-popup .rte {
    margin-top: 20px;
    margin-bottom: 0
}

@media only screen and (min-width:769px) {
    .newsletter-popup .rte {
        margin-top: 20px;
        margin-bottom: 0
    }
}

.newsletter-popup .social-icons {
    margin: 0;
    display: block;
    text-align: center
}

.newsletter-popup .social-icons li {
    display: inline-block;
    margin: 30px 15px 0 0
}

.newsletter-popup .social-icons li:last-child {
    margin-right: 0
}

.newsletter-popup .newsletter__input-group {
    margin-top: 30px
}

@media only screen and (max-width:768px) {
    .newsletter-popup .newsletter__input-group {
        margin-top: 20px
    }
}

.newsletter-button {
    margin-top: 30px
}

.password-page__logo h1 {
    margin-bottom: 0
}

.password__lock .icon {
    position: relative;
    top: -2px;
    margin-right: 4px
}

#LoginModal .modal__inner {
    background: var(--color-body);
    color: var(--color-text-body);
    padding: 50px 50px 10px
}

.password-page__toolbar {
    z-index: var(--z-index-toolbar)
}

.icon-shopify-logo {
    width: 60px;
    height: 20px
}

@media only screen and (max-width:768px) {
    .password-header-section .header-item--logo {
        width: 50%
    }
}

.product-block {
    display: block;
    margin-bottom: 20px
}

.product-block hr {
    margin: 0
}

:root {
    --z-index-promo-tint: 2;
    --z-index-promo-color-overlay: 3;
    --z-index-promo-text: 3;
    --z-index-promo-content: 4;
    --z-index-promo-link: 5
}

.promo-grid--space-top {
    padding-top: var(--index-section-padding)
}

.promo-grid--space-bottom {
    padding-bottom: var(--index-section-padding)
}

@media only screen and (max-width:768px) {
    .promo-grid--hidden-block-images .flex-grid__item.type-image {
        display: none
    }
}

.promo-grid__container {
    display: flex;
    align-items: flex-start;
    position: relative;
    background-repeat: no-repeat;
    border-radius: var(--roundness);
    overflow: hidden
}

.promo-grid__container.horizontal-center {
    justify-content: center;
    text-align: center
}

.promo-grid__container.horizontal-center .btn {
    margin: 2px 5px
}

.promo-grid__container.horizontal-left .btn {
    margin-right: 10px
}

.promo-grid__container.horizontal-right {
    justify-content: flex-end;
    text-align: right
}

.promo-grid__container.horizontal-right .btn {
    margin-left: 10px
}

.promo-grid__container .scheme-image {
    border-radius: calc(var(--roundness) + 2px)
}

.promo-grid__slide-link {
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: var(--z-index-promo-link)
}

.promo-grid__slide-link:hover~.promo-grid__content .btn:not(.btn--secondary):not(.btn--inverse) {
    background: var(--color-button-primary-light);
    transition-delay: 0.25s
}

.promo-grid__content {
    flex: 0 1 auto;
    padding: 2em 2.5em;
    position: relative;
    min-width: 200px;
    z-index: var(--z-index-promo-content)
}

.promo-grid__content p:last-child {
    margin-bottom: 0
}

.vertical-top .promo-grid__content {
    align-self: flex-start
}

.vertical-center .promo-grid__content {
    align-self: center
}

.vertical-bottom .promo-grid__content {
    align-self: flex-end
}

.video-interactable .promo-grid__content {
    pointer-events: none
}

.type-advanced .promo-grid__content:not(.promo-grid__content--sale) a,
.type-simple .promo-grid__content:not(.promo-grid__content--sale) a {
    color: #fff;
    border-bottom: 2px solid;
    border-bottom-color: #fff
}

.type-advanced .rte--strong,
.type-product .rte--strong,
.type-sale_collection .rte--strong {
    line-height: 1.1
}

.type-advanced .rte--em,
.type-product .rte--em,
.type-sale_collection .rte--em {
    font: inherit;
    text-transform: lowercase;
    font-weight: 700
}

.type-advanced .rte--strong,
.type-product .rte--strong {
    font-size: 1.8em;
    line-height: 1.1
}

@media only screen and (min-width:769px) {

    .type-advanced .rte--strong,
    .type-product .rte--strong {
        font-size: 2.25em
    }

    .type-product.flex-grid__item--50 .rte--strong,
    .type-product.flex-grid__item--33 .rte--strong {
        font-size: 1.6em
    }
}

.promo-grid__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: left
}

.promo-grid__bg .placeholder-svg {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    max-width: none;
    width: auto;
    padding: 0
}

.video-interactable .promo-grid__bg:before {
    pointer-events: none
}

.promo-grid__bg-image {
    z-index: var(--z-index-hero-image)
}

.promo-grid__text {
    position: relative
}

.promo-grid__text .btn {
    margin-top: 2px;
    margin-bottom: 2px
}

@media only screen and (min-width:769px) {
    .promo-grid>.flex-grid>.flex-grid__item--50 .enlarge-text {
        font-size: calc(var(--type-base-size) + 2px)
    }

    .promo-grid>.flex-grid>.flex-grid__item--50 .btn {
        padding: 9px 16px;
        font-size: calc(var(--type-base-size) + 1px)
    }
}

.type-advanced {
    --z-index-overlay: -1
}

.type-advanced .promo-grid__content {
    flex: 1 1 auto;
    padding: 7%
}

.type-advanced .rte--block {
    color: #fff;
    text-shadow: 0 2px 3px rgb(0 0 0 / 20%)
}

.type-advanced .btn {
    margin-bottom: 10px
}

@media only screen and (max-width:768px) {
    .type-advanced .btn {
        margin-bottom: 7px
    }
}

.type-advanced .promo-grid__content:after {
    background: radial-gradient(rgb(0 0 0 / .3) 0%, transparent 60%);
    margin: -100px -200px -100px -200px
}

.type-advanced .horizontal-center .rte--block {
    padding: 0 10%
}

.type-advanced .horizontal-left .rte--block {
    padding: 0 20% 0 0
}

.type-advanced .horizontal-right .rte--block {
    padding: 0 0 0 20%
}

.type-sale_collection {
    max-height: 600px
}

.type-sale_collection .promo-grid__container {
    align-items: center
}

.type-sale_collection .promo-grid__content {
    padding: 0;
    margin: 7%;
    flex: 0 1 auto;
    min-width: 0
}

@media only screen and (max-width:768px) {
    .type-sale_collection .promo-grid__content {
        font-size: .9em
    }
}

@media only screen and (min-width:769px) {
    .type-sale_collection .promo-grid__content:not(.promo-grid__content--small-text) {
        font-size: 1.5em
    }
}

.type-sale_collection .rte--block {
    margin-bottom: 7.5px
}

.type-sale_collection .rte--block:last-child {
    margin-bottom: 0
}

.type-sale_collection .rte--strong {
    position: relative;
    display: block;
    font-size: 6.5em;
    line-height: 1;
    white-space: nowrap;
    text-align: left
}

@media only screen and (max-width:768px) {
    .type-sale_collection .rte--strong {
        font-size: 6em
    }
}

.type-sale_collection .rte--strong sup {
    font-size: .5em;
    top: -.3em
}

.type-sale_collection .rte--em,
.type-sale_collection .enlarge-text {
    text-align: left;
    padding-left: 8px
}

@media only screen and (max-width:768px) {

    .type-sale_collection .rte--em,
    .type-sale_collection .enlarge-text {
        padding-left: 4px
    }
}

.type-sale_collection small {
    display: inline;
    font-size: .4em;
    margin-left: -1.7em;
    letter-spacing: 0
}

.type-sale-images {
    flex: 1 1 50%;
    margin: 7% 7% 7% 3%
}

.type-sale-images svg {
    display: block
}

.type-sale-images__crop {
    overflow: hidden;
    width: 100%
}

.type-simple {
    --z-index-overlay: 1
}

.type-simple .promo-grid__content {
    align-self: flex-end;
    padding: calc(var(--gutter) / 1)
}

@media only screen and (max-width:768px) {
    .type-simple .promo-grid__content {
        padding: calc(var(--gutter) / 2)
    }
}

.type-simple .promo-grid__text {
    color: #fff
}

.type-simple .promo-grid__bg:after {
    background: linear-gradient(15deg, rgb(0 0 0 / .6), transparent 40%)
}

.promo-grid__title:last-child {
    margin-bottom: 0
}

.type-image .promo-grid__container {
    background: none
}

.type-image img,
.type-image a,
.type-image .image-wrap {
    width: 100%
}

.type-banner p {
    margin: 7px;
    display: inline-block;
    vertical-align: middle;
    line-height: 1.2
}

.type-banner__link {
    display: block;
    flex: 1 1 100%
}

.type-banner__content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px
}

.type-banner__text {
    flex: 0 1 auto;
    padding: 12px;
    display: flex;
    align-items: center
}

@media only screen and (max-width:959px) {
    .type-banner__text {
        flex-wrap: wrap;
        justify-content: center
    }
}

.type-banner h2 {
    margin: 10px
}

@media only screen and (min-width:769px) {
    .type-banner__text {
        padding: 20px
    }
}

.type-product__wrapper {
    flex: 1 1 100%;
    align-self: center;
    position: relative;
    padding: 2em 0;
    z-index: var(--z-index-promo-content)
}

.promo-grid__product {
    display: flex;
    align-items: center
}

@media only screen and (min-width:769px) {
    .flex-grid__item--25 .promo-grid__product {
        flex-direction: column-reverse
    }
}

.type-product__text {
    flex: 1 1 40%
}

.promo-grid__product-images {
    position: relative;
    flex: 1 1 60%
}

@media only screen and (max-width:768px) {
    .promo-grid__product-images {
        padding-left: 1em;
        padding-right: 1em
    }
}

@media only screen and (min-width:769px) {
    .flex-grid__item--25 .promo-grid__product-images {
        width: 100%;
        margin: 0
    }
}

.promo-grid__product-text,
.promo-grid__product-images {
    margin: 6%
}

.promo-grid__product-images {
    margin-left: 0
}

.type-product__image {
    position: relative
}

.type-product__image:first-child {
    width: 100%
}

.type-product__image:nth-child(2) {
    position: absolute;
    bottom: 40px;
    right: 0;
    width: 100%;
    max-width: 40%
}

@media only screen and (max-width:768px) {
    .promo-grid__product {
        flex-wrap: wrap
    }

    .promo-grid__product-text,
    .promo-grid__product-images {
        margin: 3%
    }

    .promo-grid__product-text {
        order: 2
    }
}

.type-product__labels {
    z-index: var(--z-index-promo-text);
    display: flex;
    justify-content: center;
    gap: 1em;
    margin-bottom: 2em
}

@media only screen and (min-width:769px) {
    .type-product__labels {
        justify-content: flex-start
    }

    .type-product__labels .flex-grid__item--50,
    .type-product__labels .flex-grid__item--33 {
        font-size: .9em
    }
}

.type-product__label {
    padding: 4px 12px;
    background-color: var(--color-button-primary);
    color: var(--color-button-primary-text);
    border-radius: var(--roundness)
}

.type-product__label--secondary {
    background-color: var(--color-body);
    color: var(--color-text-body)
}

.flex-grid {
    display: flex;
    flex-wrap: wrap;
    flex: 1 1 100%
}

[data-center-text='true'] .flex-grid {
    justify-content: center
}

.flex-grid--center {
    align-items: center
}

.flex-grid--gutters {
    margin-top: calc(var(--gutter) / -2);
    margin-left: calc(var(--gutter) / -2)
}

.flex-grid__item {
    flex: 0 1 100%;
    display: flex;
    align-items: stretch
}

.flex-grid--gutters .flex-grid__item {
    padding-top: calc(var(--gutter) / 2);
    padding-left: calc(var(--gutter) / 2)
}

.flex-grid__item>* {
    flex: 1 1 100%
}

.flex-grid__item--stretch {
    flex: 1 1 100%
}

@media only screen and (min-width:769px) {
    .flex-grid__item--25 {
        flex-basis: 25%
    }

    .flex-grid__item--33 {
        flex-basis: 33.33%
    }

    .flex-grid__item--50 {
        flex-basis: 50%
    }

    .flex-grid__item--66 {
        flex-basis: 66.66%
    }

    .flex-grid__item--75 {
        flex-basis: 75%
    }
}

.background-media-text__video {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 0
}

@media only screen and (max-width:768px) {
    .background-media-text__video {
        width: 300%;
        left: -100%
    }
}

.background-media-text__video iframe,
.background-media-text__video video {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    pointer-events: none
}

@media only screen and (min-width:769px) {

    .background-media-text__video iframe,
    .background-media-text__video video {
        height: 120%;
        max-width: none;
        left: -100%;
        height: 150%;
        width: 300%
    }
}

@media screen and (min-width:1140px) {

    .background-media-text__video iframe,
    .background-media-text__video video {
        width: 100%;
        height: 300%;
        left: auto;
        top: -100%
    }
}

.video-interactable .background-media-text__video iframe,
.video-interactable .background-media-text__video video {
    pointer-events: auto
}

.loaded .background-media-text__video {
    animation: zoom-fade 1s cubic-bezier(.26, .54, .32, 1) 0s forwards;
    transition: none
}

@media only screen and (min-width:769px) {
    .background-media-text--450 .background-media-text__video {
        min-height: 450px
    }

    .background-media-text--550 .background-media-text__video {
        min-height: 550px
    }

    .background-media-text--650 .background-media-text__video {
        min-height: 650px
    }

    .background-media-text--750 .background-media-text__video {
        min-height: 750px
    }

    .background-media-text__video {
        opacity: 0
    }
}

@media only screen and (min-width:769px) {
    .rich-text-section .background-svg--wave .page-width {
        padding-bottom: 20px
    }
}

.scrolling-text {
    overflow: hidden
}

.scrolling-text__inner {
    visibility: visible;
    white-space: nowrap;
    display: inline-flex;
    margin: .15em 0;
    animation: scrolling-text var(--move-speed) linear infinite;
    animation-play-state: running
}

.scrolling-text__inner--right {
    transform: translateX(-50%);
    animation: scrolling-text-right var(--move-speed) linear infinite
}

.scrolling-text span {
    white-space: nowrap
}

.scrolling-text:hover .scrolling-text__inner,
.scrolling-text:focus .scrolling-text__inner {
    animation-play-state: paused
}

@keyframes scrolling-text {
    0% {
        transform: translateX(0);
        -webkit-transform: translateX(0)
    }

    100% {
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%)
    }
}

@keyframes scrolling-text-right {
    0% {
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%)
    }

    100% {
        transform: translateX(0);
        -webkit-transform: translateX(0)
    }
}

.results {
    padding: 0 20px 20px;
    display: block;
    width: 100%
}

.results ul {
    list-style: none;
    padding: 0;
    margin: 0
}

.results li {
    transition: background 0.2s ease;
    padding: 10px;
    margin-bottom: 0
}

.results li:hover {
    background: rgb(0 0 0 / .05)
}

.results li a {
    display: flex;
    align-items: center
}

.results--queries span {
    font-weight: bolder
}

.results--queries mark {
    background-color: #fff0;
    font-weight: 400
}

.results-products__info>span {
    margin-left: 10px
}

.results--products #predictive-search-products {
    margin-bottom: 20px
}

.results-products__image {
    width: 100%;
    min-width: 80px;
    max-width: 80px;
    height: 80px
}

.results-products__info {
    display: flex;
    flex-direction: column
}

.predictive-search-results {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    overflow-y: auto;
    padding: 40px 0 0
}

.predictive-search-results h3 {
    padding-bottom: 10px;
    border-bottom: 1px solid;
    border-color: var(--color-border);
    margin-bottom: 10px
}

.predictive-search-results--none {
    padding-top: 20px
}

.predictive-search__no-results {
    display: block;
    width: 100%
}

.predictive-search__no-results:hover {
    opacity: .5
}

.results__group-1 {
    flex: 100% 0 0
}

.results__group-1>div:not(.results--queries) {
    display: none
}

.results__group-2 {
    flex: 100% 0 0
}

.results__search-btn {
    width: 100%;
    padding: 10px;
    transition: background-color 0.2s ease;
    border: 1px solid;
    border-color: var(--color-border)
}

.results__search-btn:hover {
    background-color: rgb(0 0 0 / .05)
}

.results__search-btn .icon {
    width: 14px;
    height: 14px
}

@container (min-width:800px) {
    .predictive-search-results {
        flex-direction: row;
        flex-wrap: nowrap
    }

    .results {
        padding: 0 40px 20px
    }

    .results__group-1 {
        flex: 1 0 0%
    }

    .results__group-1 div:not(.results--queries) {
        display: block
    }

    .results__group-2 {
        flex: 2 0 0%
    }

    .results__group-2>div:not(.results--products) {
        display: none
    }

    .results__search-btn {
        text-align: left;
        padding: 10px 0 10px 40px
    }
}

.slideshow-wrapper {
    position: relative
}

.slideshow__pause:focus {
    clip: auto;
    width: auto;
    height: auto;
    margin: 0;
    color: var(--color-button-primary-text);
    background-color: var(--color-button-primary);
    padding: 10px;
    z-index: 10000;
    transition: none
}

.video-is-playing .slideshow__pause:focus {
    display: none
}

.slideshow__pause-stop {
    display: block
}

.is-paused .slideshow__pause-stop {
    display: none
}

.slideshow__pause-play {
    display: none
}

.is-paused .slideshow__pause-play {
    display: block
}

.slideshow__slide {
    display: none;
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden
}

.slideshow__slide:first-child {
    display: block
}

.flickity-slider .slideshow__slide {
    display: block
}

.hero--padded .slideshow__slide {
    border-radius: var(--roundness);
    overflow: hidden
}

.hero__image-wrapper--no-overlay:before {
    content: none
}

.hero__sidebyside {
    height: 100%;
    display: flex
}

.hero__sidebyside.color-scheme-none {
    background-color: var(--color-body);
    color: var(--color-text-body)
}

.hero__sidebyside-content {
    position: relative
}

.hero__sidebyside-content-inner {
    align-self: center;
    flex: 1;
    padding: 30px
}

.hero__sidebyside-image {
    overflow: hidden
}

.hero__sidebyside-image-link {
    display: block;
    height: 100%
}

.hero__sidebyside-image--indented .hero__image {
    padding: 30px 30px 0;
    object-fit: contain
}

.hero__sidebyside-image--indented .placeholder-svg {
    margin: 30px 30px 0;
    height: calc(100% - 60px);
    width: calc(100% - 60px)
}

@media only screen and (max-width:768px) {
    .hero__sidebyside {
        flex-direction: column
    }

    .hero__sidebyside-image {
        position: relative;
        flex: 1 1 auto
    }

    .hero__sidebyside-image .hero__image,
    .hero__sidebyside-image .placeholder-svg {
        position: absolute;
        top: 0;
        left: 0
    }

    .hero__sidebyside-content {
        flex: 0 1 auto;
        display: flex;
        order: 2
    }
}

@media only screen and (min-width:769px) {
    .hero__sidebyside-content {
        flex: 1 1 auto;
        display: flex;
        max-width: 40%
    }

    .hero__sidebyside-content-inner {
        padding: 5vw
    }

    .hero__sidebyside-text--right .hero__sidebyside-content {
        order: 2
    }

    .hero__sidebyside-text--left .hero__sidebyside-content-inner--indented {
        padding-right: 0
    }

    .hero__sidebyside-text--right .hero__sidebyside-content-inner--indented {
        padding-left: 0
    }

    .hero__sidebyside-image {
        flex: 0 1 60%
    }

    .hero__sidebyside-image--indented .hero__image {
        padding: 80px;
        object-position: center right
    }

    .hero__sidebyside-text--right .hero__sidebyside-image--indented .hero__image {
        object-position: center left
    }
}

.tool-tip-trigger {
    background: none;
    border: 0;
    cursor: pointer
}

.tool-tip-trigger__title {
    display: inline-block
}

.tool-tip-trigger__title:hover,
.tool-tip-trigger__title:focus {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-underline-offset: 2px
}

.tool-tip-trigger__content {
    display: none !important
}

.size-chart__standalone {
    margin: var(--size-chart-margin)
}

.size-chart__standalone svg {
    margin-left: var(--size-chart-icon-margin)
}

[data-tool-tip='size-chart'] .tool-tip__close {
    border: 0;
    padding: 6px;
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(25%, -25%);
    z-index: 2;
    color: var(--color-button-primary-text);
    background: var(--color-button-primary);
    border-radius: 50%
}

tool-tip {
    display: none
}

tool-tip.quick-shop-modal .page-content--product {
    max-width: 100%;
    width: 1500px
}

tool-tip .page-content,
tool-tip .page-width {
    padding: 0
}

tool-tip[data-tool-tip-open='true'] {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10001;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

tool-tip[data-tool-tip-open='true']:before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #e0e0e0;
    background-color: var(--color-modal-bg);
    animation: overlay-on 0.3s forwards;
    cursor: pointer
}

.tool-tip__inner {
    animation: modal-close 0.3s forwards;
    opacity: 0;
    color: var(--color-text-body)
}

tool-tip[data-tool-tip-open='true'] .tool-tip__inner {
    animation: modal-open 0.3s forwards;
    display: block;
    position: fixed;
    background: var(--color-body);
    box-shadow: 0 10px 20px #00000017;
    padding: 30px;
    min-width: 250px;
    min-height: 250px
}

@media only screen and (max-width:768px) {
    tool-tip[data-tool-tip-open='true'] .tool-tip__inner {
        width: calc(100% - 20px);
        max-width: 500px
    }
}

@media only screen and (min-width:769px) {
    .tool-tip__inner {
        margin: 40px;
        max-width: calc(100% - 80px);
        max-height: 90vh;
        padding: calc(var(--gutter) * 1.5)
    }

    .quick-shop-modal .tool-tip__inner {
        max-width: 1200px;
        width: calc(100% - 80px)
    }
}

.tool-tip__content {
    max-height: 80vh;
    overflow: auto
}

.quick-shop-modal .tool-tip__content,
.quick-add-modal .tool-tip__content {
    padding: 1rem
}

[data-tool-tip='store-availability'] .tool-tip__content {
    padding: 0
}

@media only screen and (max-width:768px) {
    .tool-tip__content {
        font-size: .85em
    }
}

.tool-tip__close {
    position: absolute;
    top: 0;
    right: 0;
    border: 0;
    padding: 6px;
    transform: translate(25%, -25%);
    z-index: 2;
    transition: transform 0.15s ease-out
}

.tool-tip__close:hover {
    transform: translate(25%, -25%) scale(1.08)
}

.tool-tip__close .icon {
    width: 28px;
    height: 28px
}

.store-availability {
    margin-top: var(--gutter);
    display: flex;
    justify-content: space-around
}

.store-availability .icon {
    margin: 6px 0 0;
    width: 12px;
    height: 12px
}

.store-availability .icon-in-stock {
    fill: var(--success-green)
}

.store-availability .icon-out-of-stock {
    fill: var(--error-red)
}

.store-availability+.store-availability {
    margin-top: 20px
}

.store-availability__info {
    flex: 0 1 90%;
    text-align: left;
    margin-left: 10px
}

.store-availability__info>div {
    margin-bottom: 5px
}

.store-availability__info>div:last-child {
    margin-bottom: 0
}

.store-availability__info a {
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.store-availability__small {
    font-size: .8em
}

.store-availability__small a {
    display: block;
    margin-top: 10px
}

.quick-shop-modal .store-availability__small--link {
    display: none
}

.quote-icon {
    display: block;
    margin: 0 auto 20px
}

.testimonial-stars {
    display: block;
    font-size: 16px;
    letter-spacing: .2em;
    margin-bottom: 10px
}

@media only screen and (min-width:769px) {
    .testimonial-stars {
        font-size: 18px;
        margin-bottom: 15px
    }
}

.testimonials-slide {
    display: none;
    opacity: 0;
    padding: 0 0 55px;
    width: 33%
}

.testimonials-slide:first-child {
    display: block
}

.flickity-slider .testimonials-slide {
    display: block;
    opacity: 1
}

.testimonials-slide .testimonials-slider__text {
    transform: scale(.95);
    transition: transform 0.5s ease, box-shadow 0.5s ease
}

.testimonials-slide.is-selected .testimonials-slider__text {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgb(0 0 0 / .1);
    position: relative;
    z-index: 10
}

@media only screen and (max-width:768px) {
    .testimonials-slide {
        width: 100%;
        padding: 0 0 25px
    }

    .testimonials-slide .testimonials-slider__text {
        transform: scale(.86)
    }

    .testimonials-slide.is-selected .testimonials-slider__text {
        transform: scale(1)
    }
}

.testimonials-slider__text {
    margin: 0 30px;
    padding: 30px calc(var(--gutter) / 2);
    background: var(--color-body);
    color: var(--color-text-body);
    margin-bottom: calc(var(--gutter) / 2)
}

@media only screen and (min-width:769px) {
    .testimonials-slider__text {
        margin: 0;
        padding: 30px;
        margin-bottom: 0
    }
}

.testimonials-slider__text cite {
    font-style: normal;
    font-weight: var(--type-header-weight)
}

@media only screen and (min-width:769px) {
    .testimonials-slider__text cite {
        font-size: calc(var(--type-base-size) + 1px)
    }
}

.testimonials-slider__text p {
    margin-bottom: calc(var(--gutter) / 4)
}

.testimonials-slider__text p+cite {
    margin-top: 0
}

.testimonials__info {
    font-size: calc(var(--type-base-size) - 1px)
}

:root {
    testimonial-image-size: 142px;
    testimonial-image-size-round: 65px
}

.testimonial-image {
    max-width: var(testimonial-image-size);
    background-color: var(--color-body)
}

.text-right .testimonial-image {
    margin-left: auto
}

.text-center .testimonial-image {
    margin-left: auto;
    margin-right: auto
}

.testimonial-image .image-wrap {
    background: none;
    width: 100%;
    height: 100%
}

.testimonial-image--round {
    width: var(testimonial-image-size-round);
    height: var(testimonial-image-size-round);
    max-width: none;
    border-radius: var(testimonial-image-size-round)
}

.testimonial-image--round img {
    overflow: hidden;
    border-radius: var(testimonial-image-size-round);
    height: 100%
}

.testimonials-section {
    display: block;
    padding: var(--index-section-padding) 0
}

.testimonials-section .flickity-page-dots {
    bottom: 0
}

.feature-row-wrapper {
    overflow: hidden;
    direction: ltr
}

.feature-row {
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center
}

@media (--widescreen) {
    .feature-row {
        margin: 0 6%
    }
}

@media only screen and (max-width:768px) {
    .feature-row {
        flex-direction: column;
        margin: 0
    }
}

@media only screen and (max-width:959px) {
    .feature-row--small-none {
        display: block
    }
}

.feature-row__item {
    flex: 0 1 57%;
    margin: 0 auto
}

@media only screen and (max-width:768px) {
    .feature-row__item {
        flex: 1 1 auto;
        max-width: 100%;
        min-width: 100%
    }
}

.feature-row__image {
    display: block;
    margin: 0 auto;
    order: 1
}

@media only screen and (min-width:769px) {
    .feature-row__image {
        order: 2
    }
}

.feature-row__text {
    padding: 0
}

@media only screen and (max-width:768px) {
    .feature-row__text {
        order: 2;
        margin-top: 0;
        padding: 30px 20px 0;
        padding-bottom: 0
    }
}

.feature-row__text .rte {
    margin: 0
}

.feature-row__text .btn {
    margin-top: calc(var(--gutter) / 2)
}

@media only screen and (min-width:769px) {
    .feature-row__text--left {
        padding-left: calc(var(--gutter) * 2);
        padding-right: 20px
    }

    .feature-row__text--right {
        padding-right: calc(var(--gutter) * 2);
        padding-left: 20px
    }
}

.feature-row__item--overlap-images {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 15px;
    margin: 0 0 0 -30px
}

@media only screen and (min-width:769px) {
    .feature-row__item--overlap-images {
        padding: 50px 0;
        margin: 0 auto
    }
}

.feature-row__item--overlap-images>*:first-child {
    z-index: 1;
    transform: translate(30px, 30px)
}

@media only screen and (min-width:769px) {
    .feature-row__item--overlap-images>*:first-child {
        transform: translate(50px, 50px)
    }
}

.feature-row__item--overlap-images svg {
    border: 2px solid;
    border-color: var(--color-body)
}

.feature-row__item--overlap-images .feature-row__first-image {
    width: 55%
}

.feature-row__item--overlap-images .feature-row__second-image {
    width: 60%
}

.feature-row__item--placeholder-images .placeholder-image-wrap {
    width: 50%
}

.feature-row-wrapper .feature-row__images {
    width: 100%;
    min-width: 0
}

@media only screen and (max-width:959px) {
    .feature-row-wrapper .feature-row__images {
        padding-top: 30px
    }
}

.feature-row-wrapper .feature-row__text {
    width: 100%;
    min-width: 0
}

@media only screen and (min-width:769px) {
    .feature-row-wrapper .feature-row__text {
        min-width: 43%;
        flex: 0 1 43%
    }
}

.feature-row--33 .feature-row__images {
    max-width: 50%
}

@media only screen and (min-width:769px) {
    .feature-row--33 .feature-row__images {
        max-width: none;
        flex: 0 1 33.333%
    }
}

.feature-row--50 .feature-row__images {
    max-width: 100%;
    padding-right: 20px;
    padding-left: 20px
}

@media only screen and (min-width:769px) {
    .feature-row--50 .feature-row__images {
        max-width: none;
        flex: 0 1 50%;
        padding-right: 0;
        padding-left: 0
    }
}

.feature-row--66 .feature-row__images {
    max-width: 100%;
    padding-right: 20px;
    padding-left: 20px
}

@media only screen and (min-width:769px) {
    .feature-row--66 .feature-row__images {
        max-width: none;
        flex: 0 1 66.666%;
        padding-right: 0;
        padding-left: 0
    }
}

.text-with-icons__blocks {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-left: -30px;
    margin-right: -30px
}

@media only screen and (max-width:768px) {
    .text-with-icons__blocks {
        flex-direction: column;
        margin: 0
    }
}

.has-1-per-row .text-with-icons__block {
    width: 100%
}

.has-2-per-row .text-with-icons__block {
    width: 50%
}

.has-3-per-row .text-with-icons__block {
    width: 33.333%
}

.has-4-per-row .text-with-icons__block {
    width: 25%
}

.has-5-per-row .text-with-icons__block {
    width: 20%
}

.text-with-icons__blocks .text-with-icons__block {
    display: flex;
    flex-direction: column;
    flex: none;
    padding: 30px
}

@media only screen and (max-width:768px) {
    .text-with-icons__blocks .text-with-icons__block {
        width: 100%;
        padding: 0 0 60px
    }
}

.text-with-icons__block-icon {
    display: block;
    margin-bottom: 10px
}

.text-with-icons__block-icon .icon {
    width: 70px;
    height: 70px
}

@media only screen and (max-width:768px) {
    .text-with-icons__block-icon .icon {
        width: 60px;
        height: 60px
    }
}

.text-with-icons__button {
    display: flex;
    justify-content: center
}

.text-with-icons {
    margin-bottom: -22px
}

.text-with-icons__blocks .text-with-icons__block {
    padding: 0 22px 22px
}

@media only screen and (max-width:768px) {
    .text-with-icons__blocks .text-with-icons__block {
        padding: 0 0 22px
    }
}

.text-with-icons__button {
    padding-bottom: 20px
}

.toolbar {
    position: relative;
    background: var(--color-toolbar);
    color: var(--color-toolbar-text);
    font-size: calc(var(--type-base-size) * 0.85)
}

.toolbar a {
    color: var(--color-toolbar-text)
}

.is-light .toolbar {
    background-color: #fff0;
    color: var(--color-sticky-nav-links)
}

.is-light .toolbar a {
    color: var(--color-sticky-nav-links)
}

.toolbar:after {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    content: '';
    display: block;
    border-bottom: 1px solid;
    border-bottom-color: var(--color-toolbar-text);
    width: 100%;
    opacity: .15
}

.is-light .toolbar:after {
    border-color: var(--color-sticky-nav-links)
}

.toolbar__content {
    display: flex;
    justify-content: flex-end;
    align-items: center
}

.toolbar__item {
    flex: 0 1 auto;
    padding: 0 5px
}

.toolbar__item:first-child {
    padding-left: 0
}

.toolbar__item:last-child {
    padding-right: 0
}

.toolbar__item select,
.toolbar__item .faux-select {
    font-size: 14px;
    padding-top: 5px;
    padding-bottom: 5px;
    padding-left: 0;
    border: 0;
    background: #fff0;
    color: currentColor
}

.toolbar__item--announcements {
    flex: 1 1 auto
}

.toolbar__social {
    text-align: right
}

.toolbar__social a {
    display: block;
    padding: 5px
}

.toolbar__social .icon {
    position: relative;
    vertical-align: middle;
    width: 16px;
    height: 16px
}

.social-sharing__title {
    font-size: calc(var(--type-base-size) * 0.85);
    display: inline-block;
    vertical-align: middle;
    padding-right: 15px;
    padding-left: 3px
}

.footer__social {
    margin: 0
}

form+.footer__social {
    margin-top: var(--gutter)
}

.footer__social li {
    display: inline-block;
    margin: 0 15px 15px 0
}

.footer__social a {
    display: block
}

.footer__social .icon {
    width: 21px;
    height: 21px
}

.footer__social .icon.icon--wide {
    width: 40px
}

.social-sharing {
    display: inline-flex;
    flex-wrap: wrap;
    gap: 10px 20px
}

.social-sharing .icon {
    height: 18px;
    width: 18px
}

.blog-layout__sidebar .social-sharing {
    margin-bottom: 40px
}

.social-sharing__link {
    display: inline-block;
    color: var(--color-text-body);
    border-radius: 2px;
    margin: 0 18px 0 0;
    -webkit-text-decoration: none;
    text-decoration: none;
    font-weight: 400
}

.social-sharing__link:last-child {
    margin-right: 0
}

.variant__label-info {
    text-transform: none;
    font-weight: 400;
    letter-spacing: 0
}

:root {
    --label-bottom-margin: 12px
}

.variant-button-wrap {
    border: 0;
    padding: 0;
    margin: 0 0 calc(var(--label-bottom-margin) * -1);
    position: relative
}

.variant-button-wrap label {
    font: inherit;
    position: relative;
    display: inline-block;
    font-weight: 400;
    padding: 7px 15px 7px;
    margin: 0 8px var(--label-bottom-margin) 0;
    background-color: var(--color-body);
    box-shadow: 0 0 0 1px var(--color-border);
    border-radius: 1px;
    overflow: hidden
}

.variant-button-wrap label[data-color-swatch] {
    padding: 0;
    background-color: var(--swatch-bg-color);
    background-image: var(--swatch-bg-image)
}

.variant-button-wrap input[type='radio']:focus+label {
    box-shadow: 0 0 0 1px var(--color-text-body);
    outline: 2px auto Highlight;
    outline: 2px auto -webkit-focus-ring-color;
    outline-offset: 1px
}

.variant-button-wrap input[type='radio']:checked+label {
    box-shadow: 0 0 0 2px var(--color-text-body)
}

.variant-button-wrap input[data-disabled]+label {
    color: var(--color-border)
}

.variant-button-wrap input[data-disabled]+label:after {
    position: absolute;
    content: '';
    left: 50%;
    top: 0;
    bottom: 0;
    border-left: 2px solid;
    border-color: var(--color-border);
    transform: rotate(45deg)
}

.variant-button-wrap .variant__label,
.variant-button-wrap .variant__label[for] {
    display: block;
    margin-bottom: 10px;
    cursor: default
}

.variant-wrapper+.variant-wrapper {
    margin-top: var(--size-6)
}

video-media {
    --default-aspect-ratio: 16 / 9;
    display: block;
    position: relative;
    height: 100%;
    aspect-ratio: var(--aspect-ratio, var(--default-aspect-ratio))
}

video-media>video,
video-media>iframe,
video-media>img,
video-media>svg {
    border-radius: inherit;
    width: 100%;
    height: 100%;
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out
}

video-media>img,
video-media>svg,
video-media>video:not(:fullscreen) {
    object-fit: cover;
    object-position: center
}

.video-media[data-background='true']>video-media {
    position: absolute;
    height: 100%;
    width: 100%
}

video-media:not([loaded])>video,
video-media:not([loaded])>iframe,
video-media[loaded]>img,
video-media[loaded]>svg {
    visibility: hidden;
    opacity: 0
}