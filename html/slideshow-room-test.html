<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Slideshow Room Test</title>
  <style>
      body {
          font-family: Arial, sans-serif;
          background: #f5f5f5;
          margin: 0;
          padding: 20px;
      }

      h1 {
          text-align: center;
          color: #333;
          margin-bottom: 30px;
      }

      /* Simulating the hero section */
      .hero {
          height: 650px;
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 4px 8px rgba(0,0,0,0.1);
          overflow: hidden;
      }

      /* Dual images layout styles */
      .hero__dual-images {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 58px;
          padding: 0 20px;
      }

      .hero__left-image {
          width: 647px;
          height: 647px;
          overflow: hidden;
          flex-shrink: 0;
          background: #e0e0e0;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #666;
          font-size: 18px;
          position: relative;
      }

      .hero__right-image {
          width: 1015px;
          height: 647px;
          overflow: hidden;
          flex-shrink: 0;
          background: #d0d0d0;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #666;
          font-size: 18px;
          position: relative;
      }

      .hero__left-image img,
      .hero__right-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
      }

      .hero__image-link {
          display: block;
          width: 100%;
          height: 100%;
          text-decoration: none;
          color: inherit;
      }

      .hero__image-link:hover {
          opacity: 0.9;
      }

      /* Size labels for testing */
      .size-label {
          position: absolute;
          top: 10px;
          left: 10px;
          background: rgba(0,0,0,0.7);
          color: white;
          padding: 5px 10px;
          border-radius: 4px;
          font-size: 14px;
      }

      @media screen and (max-width: 1200px) {
          .hero__dual-images {
              gap: 30px;
              padding: 0 15px;
          }
          
          .hero__left-image {
              width: 400px;
              height: 400px;
          }

          .hero__right-image {
              width: 600px;
              height: 400px;
          }
      }

      @media screen and (max-width: 768px) {
          .hero {
              height: auto;
              min-height: 450px;
          }

          .hero__dual-images {
              flex-direction: column;
              gap: 20px;
              padding: 20px;
          }

          .hero__left-image,
          .hero__right-image {
              width: 100%;
              height: 250px;
          }
      }

      .test-info {
          background: #fff;
          padding: 20px;
          margin-bottom: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .test-info h2 {
          color: #333;
          margin-top: 0;
      }

      .test-info ul {
          color: #666;
          line-height: 1.6;
      }
  </style>
</head>
<body>
  <h1>Slideshow Room Layout Test</h1>
  
  <div class="test-info">
    <h2>Layout Specifications</h2>
    <ul>
      <li>Left Image: 647px × 647px (square)</li>
      <li>Right Image: 1015px × 647px (rectangular)</li>
      <li>Gap between images: 58px</li>
      <li>Responsive design for tablet and mobile</li>
      <li>Both images are clickable links</li>
    </ul>
  </div>

  <div class="hero">
    <div class="hero__dual-images">
      <div class="hero__left-image">
        <div class="size-label">647×647px</div>
        <a href="#" class="hero__image-link">
          Left Image<br>
          (Square Format)
        </a>
      </div>
      <div class="hero__right-image">
        <div class="size-label">1015×647px</div>
        <a href="#" class="hero__image-link">
          Right Image<br>
          (Rectangular Format)
        </a>
      </div>
    </div>
  </div>

  <div class="test-info" style="margin-top: 20px;">
    <h2>Test Instructions</h2>
    <ul>
      <li>Resize your browser window to test responsive behavior</li>
      <li>On desktop: Images should maintain their specified sizes with 58px gap</li>
      <li>On tablet (≤1200px): Images should scale down proportionally with 30px gap</li>
      <li>On mobile (≤768px): Images should stack vertically with 20px gap</li>
      <li>Both image areas should be clickable</li>
    </ul>
  </div>
</body>
</html>
